/**
 * Profile Settings Utilities
 * 
 * This file provides utility functions and examples for using the profile settings endpoints:
 * - Student Profile Settings: /api/profile/students/{studentId}
 * - Tutor Profile Settings: /api/profile/tutors/{tutorId}
 */

import { toast } from "react-toastify";

/**
 * Student Profile Settings Helper
 * 
 * Example usage of the student profile settings endpoint
 * Endpoint: PUT /api/profile/students/{studentId}
 */
export const updateStudentProfileSettings = async (
  updateStudentProfileMutation,
  studentId,
  profileData
) => {
  try {
    const response = await updateStudentProfileMutation({
      studentId,
      ...profileData,
    }).unwrap();

    toast.success("Student profile updated successfully!");
    return response;
  } catch (error) {
    console.error("Error updating student profile:", error);
    toast.error(error?.data?.message || "Failed to update student profile");
    throw error;
  }
};

/**
 * Tutor Profile Settings Helper
 * 
 * Example usage of the tutor profile settings endpoint
 * Endpoint: PUT /api/profile/tutors/{tutorId}
 */
export const updateTutorProfileSettings = async (
  updateTutorProfileMutation,
  tutorId,
  profileData
) => {
  try {
    const response = await updateTutorProfileMutation({
      tutorId,
      ...profileData,
    }).unwrap();

    toast.success("Tutor profile updated successfully!");
    return response;
  } catch (error) {
    console.error("Error updating tutor profile:", error);
    toast.error(error?.data?.message || "Failed to update tutor profile");
    throw error;
  }
};

/**
 * Example Student Profile Data Structure
 */
export const exampleStudentProfileData = {
  firstname: "John",
  lastname: "Doe",
  phone: "+1234567890",
  timeZone: "America/New_York",
  countryOfBirth: "United States",
  // Add other student-specific fields as needed
};

/**
 * Example Tutor Profile Data Structure
 */
export const exampleTutorProfileData = {
  firstname: "Jane",
  lastname: "Smith",
  phone: "+1234567890",
  timeZone: "America/New_York",
  countryOfBirth: "United States",
  languages: [
    { name: "English", level: "Native" },
    { name: "Spanish", level: "Fluent" }
  ],
  teachingSubjects: [
    { title: "Mathematics" },
    { title: "Physics" }
  ],
  aboutMe: "Experienced tutor with 5+ years of teaching experience...",
  headline: "Expert Math and Physics Tutor",
  motivatePotentialStudent: "I help students achieve their academic goals...",
  teachingExperience: "5 years of teaching experience in mathematics and physics...",
  basePrice: 50, // Price in dollars
  currency: "USD",
  introVideo: "base64_video_string_or_youtube_url",
  certificates: [
    {
      title: "Mathematics Teaching Certificate",
      institution: "University of Education",
      year: "2020"
    }
  ],
  // Add other tutor-specific fields as needed
};

/**
 * Validate Student Profile Data
 */
export const validateStudentProfileData = (data) => {
  const errors = {};

  if (!data.firstname?.trim()) {
    errors.firstname = "First name is required";
  }

  if (!data.lastname?.trim()) {
    errors.lastname = "Last name is required";
  }

  if (!data.timeZone) {
    errors.timeZone = "Time zone is required";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Validate Tutor Profile Data
 */
export const validateTutorProfileData = (data) => {
  const errors = {};

  if (!data.firstname?.trim()) {
    errors.firstname = "First name is required";
  }

  if (!data.lastname?.trim()) {
    errors.lastname = "Last name is required";
  }

  if (!data.timeZone) {
    errors.timeZone = "Time zone is required";
  }

  if (!data.languages || data.languages.length === 0) {
    errors.languages = "At least one language is required";
  }

  if (!data.teachingSubjects || data.teachingSubjects.length === 0) {
    errors.teachingSubjects = "At least one teaching subject is required";
  }

  if (data.basePrice && (isNaN(data.basePrice) || data.basePrice <= 0)) {
    errors.basePrice = "Base price must be a positive number";
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};

/**
 * Format profile data for API submission
 */
export const formatProfileDataForAPI = (data, userType) => {
  const formattedData = { ...data };

  // Remove empty strings and null values
  Object.keys(formattedData).forEach(key => {
    if (formattedData[key] === "" || formattedData[key] === null) {
      delete formattedData[key];
    }
  });

  // Format specific fields based on user type
  if (userType === "tutor") {
    // Ensure languages array is properly formatted
    if (formattedData.languages) {
      formattedData.languages = formattedData.languages.filter(
        lang => lang.name && lang.level
      );
    }

    // Ensure teaching subjects array is properly formatted
    if (formattedData.teachingSubjects) {
      formattedData.teachingSubjects = formattedData.teachingSubjects.filter(
        subject => subject.title
      );
    }

    // Convert price to cents if needed (depending on backend requirements)
    if (formattedData.basePrice) {
      formattedData.basePrice = Number(formattedData.basePrice);
    }
  }

  return formattedData;
};

/**
 * Handle profile update response
 */
export const handleProfileUpdateResponse = (response, userType) => {
  if (response?.success) {
    const message = `${userType === "student" ? "Student" : "Tutor"} profile updated successfully!`;
    toast.success(message);
    
    // Handle any additional response data
    if (response.details?.needReview && response.details.needReview.length > 0) {
      toast.info("Some changes require admin review and will be processed shortly.");
    }
    
    return response.data;
  } else {
    throw new Error(response?.message || "Failed to update profile");
  }
};
