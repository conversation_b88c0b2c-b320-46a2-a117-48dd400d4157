import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs) {
	return twMerge(clsx(inputs));
}

export const capitalizeWords = (str) => {
	const words = str?.split(" ");

	const capitalizedWords = words?.map((word) => {
		if (word.length === 0) {
			return "";
		}
		return word[0].toUpperCase() + word.slice(1);
	});

	return capitalizedWords?.join(" ");
};

export const convertToBase64 = (file) => {
	return new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.readAsDataURL(file);
		reader.onload = () => resolve(reader.result);
		reader.onerror = (error) => reject(error);
	});
};

// Utility function to convert UTC to local time
export const formatDisplayTime = (dateString) => {
	const date = new Date(dateString);
	const localTime = new Date(date.getTime() + date.getTimezoneOffset() * 60000);
	return localTime.toLocaleTimeString("en-US", {
		hour: "2-digit",
		minute: "2-digit",
		hour12: false,
	});
};

export const formatTutorName = (fullName) => {
	if (!fullName) return "";

	const names = fullName.trim().split(" ");

	if (names.length === 0) return "";

	// First name - keep as is (assuming it's already properly capitalized)
	const firstName = names[0][0].toUpperCase() + names[0].slice(1).toLowerCase();

	// Last name - just first letter capitalized followed by dot
	let lastNameInitial = "";
	if (names.length > 1) {
		lastNameInitial = names[names.length - 1][0]?.toUpperCase() + ".";
	}

	return [firstName, lastNameInitial].filter(Boolean).join(" ");
};

export const timezones = [
	{ value: "Africa/Cairo", label: "Cairo (UTC+2)" },
	{ value: "Africa/Johannesburg", label: "Johannesburg (UTC+2)" },
	{ value: "Africa/Lagos", label: "Lagos (UTC+1)" },
	{ value: "America/Argentina/Buenos_Aires", label: "Buenos Aires (UTC-3)" },
	{ value: "America/Chicago", label: "Chicago (UTC-6/-5)" },
	{ value: "America/Denver", label: "Denver (UTC-7/-6)" },
	{ value: "America/Los_Angeles", label: "Los Angeles (UTC-8/-7)" },
	{ value: "America/Mexico_City", label: "Mexico City (UTC-6/-5)" },
	{ value: "America/New_York", label: "New York (UTC-5/-4)" },
	{ value: "America/Phoenix", label: "Phoenix (UTC-7)" },
	{ value: "America/Sao_Paulo", label: "São Paulo (UTC-3/-2)" },
	{ value: "America/Toronto", label: "Toronto (UTC-5/-4)" },
	{ value: "America/Vancouver", label: "Vancouver (UTC-8/-7)" },
	{ value: "Asia/Bangkok", label: "Bangkok (UTC+7)" },
	{ value: "Asia/Beijing", label: "Beijing (UTC+8)" },
	{ value: "Asia/Dubai", label: "Dubai (UTC+4)" },
	{ value: "Asia/Hong_Kong", label: "Hong Kong (UTC+8)" },
	{ value: "Asia/Jakarta", label: "Jakarta (UTC+7)" },
	{ value: "Asia/Karachi", label: "Karachi (UTC+5)" },
	{ value: "Asia/Kolkata", label: "Kolkata (UTC+5:30)" },
	{ value: "Asia/Riyadh", label: "Riyadh (UTC+3)" },
	{ value: "Asia/Seoul", label: "Seoul (UTC+9)" },
	{ value: "Asia/Shanghai", label: "Shanghai (UTC+8)" },
	{ value: "Asia/Singapore", label: "Singapore (UTC+8)" },
	{ value: "Asia/Taipei", label: "Taipei (UTC+8)" },
	{ value: "Asia/Tokyo", label: "Tokyo (UTC+9)" },
	{ value: "Australia/Adelaide", label: "Adelaide (UTC+9:30/+10:30)" },
	{ value: "Australia/Brisbane", label: "Brisbane (UTC+10)" },
	{ value: "Australia/Melbourne", label: "Melbourne (UTC+10/+11)" },
	{ value: "Australia/Perth", label: "Perth (UTC+8)" },
	{ value: "Australia/Sydney", label: "Sydney (UTC+10/+11)" },
	{ value: "Canada/Atlantic", label: "Atlantic (UTC-4/-3)" },
	{ value: "Europe/Amsterdam", label: "Amsterdam (UTC+1/+2)" },
	{ value: "Europe/Athens", label: "Athens (UTC+2/+3)" },
	{ value: "Europe/Berlin", label: "Berlin (UTC+1/+2)" },
	{ value: "Europe/Dublin", label: "Dublin (UTC+0/+1)" },
	{ value: "Europe/Istanbul", label: "Istanbul (UTC+3)" },
	{ value: "Europe/Lisbon", label: "Lisbon (UTC+0/+1)" },
	{ value: "Europe/London", label: "London (UTC+0/+1)" },
	{ value: "Europe/Madrid", label: "Madrid (UTC+1/+2)" },
	{ value: "Europe/Moscow", label: "Moscow (UTC+3)" },
	{ value: "Europe/Paris", label: "Paris (UTC+1/+2)" },
	{ value: "Europe/Rome", label: "Rome (UTC+1/+2)" },
	{ value: "Pacific/Auckland", label: "Auckland (UTC+12/+13)" },
	{ value: "Pacific/Honolulu", label: "Honolulu (UTC-10)" },
	{ value: "US/Alaska", label: "Alaska (UTC-9/-8)" },
	{ value: "US/Central", label: "Central (UTC-6/-5)" },
	{ value: "US/Eastern", label: "Eastern (UTC-5/-4)" },
	{ value: "US/Mountain", label: "Mountain (UTC-7/-6)" },
	{ value: "US/Pacific", label: "Pacific (UTC-8/-7)" },
];
