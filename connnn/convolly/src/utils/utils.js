import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export const capitalizeWords = (str) => {
  const words = str?.split(" ");

  const capitalizedWords = words?.map((word) => {
    if (word.length === 0) {
      return "";
    }
    return word[0].toUpperCase() + word.slice(1);
  });

  return capitalizedWords?.join(" ");
};

export const convertToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};
