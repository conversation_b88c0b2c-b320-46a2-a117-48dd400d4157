const textEncoder = new TextEncoder();
const textDecoder = new TextDecoder();

export const arrayBufferToBase64 = (buffer) => {
  const bytes = new Uint8Array(buffer);
  let binary = "";
  const chunkSize = 0x8000; // 32KB
  for (let i = 0; i < bytes.length; i += chunkSize) {
    binary += String.fromCharCode(...bytes.subarray(i, i + chunkSize));
  }
  return btoa(binary);
};

export const base64ToArrayBuffer = (base64) => {
  const binary = atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
};

const base64ToUint8Array = (base64) => {
  const binaryString = atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
};

export const generateKeyPair = async () => {
  return crypto.subtle.generateKey(
    {
      name: "RSA-OAEP",
      modulusLength: 2048,
      publicExponent: new Uint8Array([1, 0, 1]),
      hash: "SHA-256",
    },
    true,
    ["encrypt", "decrypt"]
  );
};

export const deriveKeyFromPassword = async (password, salt) => {
  const keyMaterial = await crypto.subtle.importKey(
    "raw",
    textEncoder.encode(password),
    "PBKDF2",
    false,
    ["deriveKey"]
  );

  return crypto.subtle.deriveKey(
    {
      name: "PBKDF2",
      salt,
      iterations: 100000,
      hash: "SHA-256",
    },
    keyMaterial,
    { name: "AES-GCM", length: 256 },
    true,
    ["encrypt", "decrypt"]
  );
};

export const encryptPrivateKey = async (privateKey, password) => {
  const salt = crypto.getRandomValues(new Uint8Array(16));
  const iv = crypto.getRandomValues(new Uint8Array(12));
  const aesKey = await deriveKeyFromPassword(password, salt);
  const exported = await crypto.subtle.exportKey("pkcs8", privateKey);

  const encrypted = await crypto.subtle.encrypt(
    { name: "AES-GCM", iv },
    aesKey,
    exported
  );

  return {
    encryptedPrivateKey: btoa(
      String.fromCharCode(...new Uint8Array(encrypted))
    ),
    iv: btoa(String.fromCharCode(...iv)),
    salt: btoa(String.fromCharCode(...salt)),
  };
};

export const decryptPrivateKey = async (
  encryptedBase64,
  password,
  ivBase64,
  saltBase64
) => {
  const salt = Uint8Array.from(atob(saltBase64), (c) => c.charCodeAt(0));
  const iv = Uint8Array.from(atob(ivBase64), (c) => c.charCodeAt(0));
  const encrypted = Uint8Array.from(atob(encryptedBase64), (c) =>
    c.charCodeAt(0)
  );

  const aesKey = await deriveKeyFromPassword(password, salt);
  const decrypted = await crypto.subtle.decrypt(
    { name: "AES-GCM", iv },
    aesKey,
    encrypted
  );

  return crypto.subtle.importKey(
    "pkcs8",
    decrypted,
    { name: "RSA-OAEP", hash: "SHA-256" },
    true,
    ["decrypt"]
  );
};

export const exportPublicKey = async (publicKey) => {
  const raw = await crypto.subtle.exportKey("spki", publicKey);
  return btoa(String.fromCharCode(...new Uint8Array(raw)));
};

export const encryptText = async (publicKeyBase64, text) => {
  if (!text) return "";

  const keyBuffer = Uint8Array.from(atob(publicKeyBase64), (c) =>
    c.charCodeAt(0)
  );
  const key = await crypto.subtle.importKey(
    "spki",
    keyBuffer.buffer,
    { name: "RSA-OAEP", hash: "SHA-256" },
    true,
    ["encrypt"]
  );
  const encoded = textEncoder.encode(text);
  const encrypted = await crypto.subtle.encrypt(
    { name: "RSA-OAEP" },
    key,
    encoded
  );
  return btoa(String.fromCharCode(...new Uint8Array(encrypted)));
};

export const decryptText = async (privateKeyBase64, encryptedMessage) => {
  if (!encryptedMessage) return "";

  const privateKey = await importPrivateKey(privateKeyBase64);

  const buffer = Uint8Array.from(atob(encryptedMessage), (c) =>
    c.charCodeAt(0)
  );
  const decrypted = await crypto.subtle.decrypt(
    { name: "RSA-OAEP" },
    privateKey,
    buffer
  );

  return textDecoder.decode(decrypted);
};

export const exportPrivateKey = async (privateKey) => {
  const rawKey = await crypto.subtle.exportKey("pkcs8", privateKey);
  return btoa(String.fromCharCode(...new Uint8Array(rawKey)));
};

export const importPrivateKey = async (base64Key) => {
  const rawKey = Uint8Array.from(atob(base64Key), (c) => c.charCodeAt(0));

  return await crypto.subtle.importKey(
    "pkcs8",
    rawKey.buffer,
    { name: "RSA-OAEP", hash: "SHA-256" },
    true,
    ["decrypt"]
  );
};

const importPublicKey = async (base64Key) => {
  const binaryDer = Uint8Array.from(atob(base64Key), (c) => c.charCodeAt(0));
  return crypto.subtle.importKey(
    "spki",
    binaryDer.buffer,
    {
      name: "RSA-OAEP",
      hash: "SHA-256",
    },
    true,
    ["encrypt"]
  );
};

export const encryptFile = async (file, recipientPublicKeyBase64) => {
  const recipientPublicKey = await importPublicKey(recipientPublicKeyBase64);

  // 1. Generate AES key
  const aesKey = await window.crypto.subtle.generateKey(
    { name: "AES-GCM", length: 256 },
    true,
    ["encrypt", "decrypt"]
  );

  // 2. Encrypt AES key using recipient's RSA public key
  const encryptedAesKey = await window.crypto.subtle.encrypt(
    { name: "RSA-OAEP" },
    recipientPublicKey,
    await window.crypto.subtle.exportKey("raw", aesKey)
  );

  // 3. Encrypt file in chunks
  const chunkSize = 64 * 1024; // 64KB
  const encryptedChunks = [];
  const iv = window.crypto.getRandomValues(new Uint8Array(12));

  let offset = 0;
  while (offset < file.size) {
    const chunk = file.slice(offset, offset + chunkSize);
    const chunkBuffer = await chunk.arrayBuffer();

    const encryptedChunk = await window.crypto.subtle.encrypt(
      { name: "AES-GCM", iv },
      aesKey,
      chunkBuffer
    );

    encryptedChunks.push(new Uint8Array(encryptedChunk));
    offset += chunkSize;
  }

  return {
    encryptedKey: new Uint8Array(encryptedAesKey),
    chunks: encryptedChunks,
    iv,
  };
};

export const decryptFile = async (fileMessage, privateKeyBase64) => {
  fileMessage = {
    encryptedKey: base64ToUint8Array(fileMessage.encryptedKey),
    iv: base64ToUint8Array(fileMessage.iv),
    chunks: fileMessage.chunks.map((chunkBase64) =>
      base64ToUint8Array(chunkBase64)
    ),
  };

  const privateKey = await importPrivateKey(privateKeyBase64);

  // 1. Decrypt AES key
  const rawKey = await window.crypto.subtle.decrypt(
    { name: "RSA-OAEP" },
    privateKey,
    fileMessage.encryptedKey
  );

  const aesKey = await window.crypto.subtle.importKey(
    "raw",
    rawKey,
    { name: "AES-GCM" },
    false,
    ["decrypt"]
  );

  // 2. Decrypt chunks
  const decryptedChunks = [];
  for (const chunk of fileMessage.chunks) {
    const decryptedChunk = await window.crypto.subtle.decrypt(
      { name: "AES-GCM", iv: fileMessage.iv },
      aesKey,
      new Uint8Array(chunk)
    );
    decryptedChunks.push(new Uint8Array(decryptedChunk));
  }

  // 3. Reconstruct file
  return new Blob(decryptedChunks);
};

export const decryptRecipientFile = async (
  receiver,
  fileMessage,
  type = "blob"
) => {
  const decrypted = await decryptFile(
    fileMessage.recipients[receiver.id],
    receiver.privateKey
  );

  const blob = new Blob([decrypted], { type: fileMessage.mimetype });

  const source =
    type === "blob"
      ? blob
      : new File([blob], `${fileMessage.name}.${fileMessage.extention}`, {
          type: fileMessage.mimetype,
          lastModified: Date.now(),
        });

  return {
    source,
    name: fileMessage.name,
    mimetype: fileMessage.mimetype,
    extention: fileMessage.extention,
    // url: URL.createObjectURL(source),
  };
};
