import { 
  format, 
  addDays, 
  startOfWeek, 
  parse, 
  isAfter, 
  isBefore, 
  addMinutes,
  startOfDay,
  endOfDay,
  isWithinInterval,
  parseISO
} from "date-fns";

/**
 * Calculate the next available time slots for a tutor
 * @param {Object} tutor - Tutor object with timeAvailable array
 * @param {Array} bookedSlots - Array of booked time slots from calendar
 * @param {number} daysToCheck - Number of days ahead to check (default: 7)
 * @param {number} maxSlots - Maximum number of slots to return (default: 5)
 * @returns {Array} Array of available time slots
 */
export const getNextAvailableSlots = (tutor, bookedSlots = [], daysToCheck = 7, maxSlots = 5) => {
  if (!tutor?.timeAvailable || tutor.timeAvailable.length === 0) {
    return [];
  }

  const dayMap = {
    Sunday: 0,
    Monday: 1,
    Tuesday: 2,
    Wednesday: 3,
    Thursday: 4,
    Friday: 5,
    Saturday: 6,
  };

  const availableSlots = [];
  const now = new Date();
  const currentTime = new Date();

  // Convert tutor's timeAvailable to time slots
  const tutorAvailability = tutor.timeAvailable.reduce((acc, slot) => {
    const dayIndex = dayMap[slot.label];
    if (dayIndex === undefined || !slot.from || !slot.to) return acc;

    const start = parse(slot.from, "HH:mm", new Date());
    const end = parse(slot.to, "HH:mm", new Date());
    let current = start;

    while (current < end) {
      acc.push({
        day: dayIndex,
        hour: current.getHours(),
        minutes: current.getMinutes(),
      });
      current = addMinutes(current, 30); // 30-minute intervals
    }
    return acc;
  }, []);

  // Check each day for the next week
  for (let dayOffset = 0; dayOffset < daysToCheck && availableSlots.length < maxSlots; dayOffset++) {
    const checkDate = addDays(now, dayOffset);
    const dayOfWeek = checkDate.getDay();

    // Get available times for this day of week
    const dayAvailability = tutorAvailability.filter(slot => slot.day === dayOfWeek);

    for (const timeSlot of dayAvailability) {
      if (availableSlots.length >= maxSlots) break;

      const slotDateTime = new Date(checkDate);
      slotDateTime.setHours(timeSlot.hour, timeSlot.minutes, 0, 0);

      // Skip past time slots for today
      if (dayOffset === 0 && slotDateTime <= currentTime) {
        continue;
      }

      // Check if this slot conflicts with booked appointments
      if (!isSlotBooked(slotDateTime, bookedSlots)) {
        availableSlots.push({
          date: slotDateTime,
          dayName: format(slotDateTime, 'EEE'),
          time: format(slotDateTime, 'HH:mm'),
          displayTime: format(slotDateTime, 'h:mm a'),
          fullDisplay: format(slotDateTime, 'EEE, MMM d • h:mm a'),
        });
      }
    }
  }

  return availableSlots;
};

/**
 * Check if a time slot conflicts with booked appointments
 * @param {Date} slotDateTime - The time slot to check
 * @param {Array} bookedSlots - Array of booked appointments
 * @param {number} slotDuration - Duration of the slot in minutes (default: 30)
 * @returns {boolean} True if the slot is booked/conflicts
 */
export const isSlotBooked = (slotDateTime, bookedSlots = [], slotDuration = 30) => {
  if (!bookedSlots || bookedSlots.length === 0) return false;

  const slotStart = new Date(slotDateTime);
  const slotEnd = addMinutes(slotStart, slotDuration);

  return bookedSlots.some((booked) => {
    try {
      const bookedStart = parseISO(booked.startDateTime);
      const bookedEnd = parseISO(booked.endDateTime);

      // Check for overlap
      return (
        (slotStart >= bookedStart && slotStart < bookedEnd) ||
        (slotEnd > bookedStart && slotEnd <= bookedEnd) ||
        (slotStart <= bookedStart && slotEnd >= bookedEnd)
      );
    } catch (error) {
      console.warn('Error parsing booked slot date:', booked, error);
      return false;
    }
  });
};

/**
 * Format availability for display in tutor card
 * @param {Array} availableSlots - Array of available time slots
 * @param {number} maxDisplay - Maximum number of slots to display (default: 3)
 * @returns {Object} Formatted availability data
 */
export const formatAvailabilityForCard = (availableSlots, maxDisplay = 3) => {
  if (!availableSlots || availableSlots.length === 0) {
    return {
      hasAvailability: false,
      message: "No upcoming availability",
      slots: []
    };
  }

  const displaySlots = availableSlots.slice(0, maxDisplay);
  const hasMore = availableSlots.length > maxDisplay;

  return {
    hasAvailability: true,
    message: hasMore ? `+${availableSlots.length - maxDisplay} more` : null,
    slots: displaySlots,
    totalAvailable: availableSlots.length
  };
};

/**
 * Get tutor's next available day and time
 * @param {Object} tutor - Tutor object
 * @param {Array} bookedSlots - Array of booked slots
 * @returns {Object|null} Next available slot or null
 */
export const getNextAvailableSlot = (tutor, bookedSlots = []) => {
  const slots = getNextAvailableSlots(tutor, bookedSlots, 14, 1);
  return slots.length > 0 ? slots[0] : null;
};

/**
 * Check if tutor is available today
 * @param {Object} tutor - Tutor object
 * @param {Array} bookedSlots - Array of booked slots
 * @returns {boolean} True if tutor has availability today
 */
export const isAvailableToday = (tutor, bookedSlots = []) => {
  const todaySlots = getNextAvailableSlots(tutor, bookedSlots, 1, 1);
  return todaySlots.length > 0;
};

/**
 * Get availability status text for tutor card
 * @param {Object} tutor - Tutor object
 * @param {Array} bookedSlots - Array of booked slots
 * @returns {string} Status text
 */
export const getAvailabilityStatus = (tutor, bookedSlots = []) => {
  if (isAvailableToday(tutor, bookedSlots)) {
    return "Available today";
  }
  
  const nextSlot = getNextAvailableSlot(tutor, bookedSlots);
  if (nextSlot) {
    const daysFromNow = Math.ceil((nextSlot.date - new Date()) / (1000 * 60 * 60 * 24));
    if (daysFromNow === 1) {
      return "Available tomorrow";
    } else if (daysFromNow <= 7) {
      return `Available ${nextSlot.dayName}`;
    } else {
      return "Available next week";
    }
  }
  
  return "Limited availability";
};
