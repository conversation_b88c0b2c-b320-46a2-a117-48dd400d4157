import { generalApiSlice } from "../apiSlice";

const authApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // sign up
    localSignUp: builder.mutation({
      query: (body) => ({
        url: `/api/auth/register`,
        method: "POST",
        body
      })
    }),

    // login
    login: builder.mutation({
      query: (body) => ({
        url: `/api/auth/login`,
        method: "POST",
        body
      })
    }),

     // fotgot password
     forgotPassword: builder.mutation({
      query: (body) => ({
        url: `/api/auth/forgot-password`,
        method: "POST",
        body
      })
    }),
  }),

  overrideExisting: false
});

export const { useLocalSignUpMutation, useLoginMutation, useForgotPasswordMutation } = authApiSlice;
