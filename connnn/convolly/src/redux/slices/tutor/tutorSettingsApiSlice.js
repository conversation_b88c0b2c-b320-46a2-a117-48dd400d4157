import { generalApiSlice } from "../../apiSlice";

const tutorSettingsApiSlice = generalApiSlice.injectEndpoints({
	endpoints: (builder) => ({
		// Update tutor profile settings using the specified endpoint
		updateTutorProfileSettings: builder.mutation({
			query: ({ tutorId, ...body }) => ({
				url: `/api/profile/tutors/${tutorId}`,
				method: "PUT",
				body,
			}),
			invalidatesTags: (result, error, { tutorId }) => [
				{ type: "Profile", id: `tutor-${tutorId}` },
				{ type: "TutorCompleteProfile" },
				{ type: "Profile", id: "LIST" },
			],
		}),

		// Get tutor profile for settings
		getTutorProfileSettings: builder.query({
			query: (tutorId) => ({
				url: `/api/profile/tutors/${tutorId}`,
				method: "GET",
			}),
			providesTags: (result, error, tutorId) => [
				{ type: "Profile", id: `tutor-${tutorId}` },
			],
		}),

		// Get complete tutor profile with statistics
		getTutorCompleteProfile: builder.query({
			query: () => ({
				url: `/api/profile/tutors/my-profile`,
				method: "GET",
			}),
			providesTags: ["TutorCompleteProfile"],
		}),

		// Update tutor description/about section
		updateTutorDescription: builder.mutation({
			query: ({ tutorId, aboutMe, headline, motivatePotentialStudent }) => ({
				url: `/api/profile/tutors/${tutorId}`,
				method: "PUT",
				body: {
					aboutMe,
					headline,
					motivatePotentialStudent,
				},
			}),
			invalidatesTags: (result, error, { tutorId }) => [
				{ type: "Profile", id: `tutor-${tutorId}` },
				{ type: "TutorCompleteProfile" },
			],
		}),

		// Update tutor video
		updateTutorVideo: builder.mutation({
			query: ({ tutorId, introVideo }) => ({
				url: `/api/profile/tutors/${tutorId}`,
				method: "PUT",
				body: {
					introVideo,
				},
			}),
			invalidatesTags: (result, error, { tutorId }) => [
				{ type: "Profile", id: `tutor-${tutorId}` },
				{ type: "TutorCompleteProfile" },
			],
		}),

		// Update tutor pricing
		updateTutorPricing: builder.mutation({
			query: ({ tutorId, basePrice, currency }) => ({
				url: `/api/profile/tutors/${tutorId}`,
				method: "PUT",
				body: {
					basePrice,
					currency,
				},
			}),
			invalidatesTags: (result, error, { tutorId }) => [
				{ type: "Profile", id: `tutor-${tutorId}` },
				{ type: "TutorCompleteProfile" },
			],
		}),

		// Update tutor background/experience
		updateTutorBackground: builder.mutation({
			query: ({ tutorId, teachingExperience, certificates, teachingSubjects }) => ({
				url: `/api/profile/tutors/${tutorId}`,
				method: "PUT",
				body: {
					teachingExperience,
					certificates,
					teachingSubjects,
				},
			}),
			invalidatesTags: (result, error, { tutorId }) => [
				{ type: "Profile", id: `tutor-${tutorId}` },
				{ type: "TutorCompleteProfile" },
			],
		}),
	}),

	overrideExisting: false,
});

export const {
	useUpdateTutorProfileSettingsMutation,
	useGetTutorProfileSettingsQuery,
	useGetTutorCompleteProfileQuery,
	useUpdateTutorDescriptionMutation,
	useUpdateTutorVideoMutation,
	useUpdateTutorPricingMutation,
	useUpdateTutorBackgroundMutation,
} = tutorSettingsApiSlice;
