import { generalApiSlice } from "../apiSlice";

const calendarApiSlice = generalApiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Create a new calendar
    createCalendar: builder.mutation({
      query: (body) => ({
        url: `/api/calendars`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["Calendar"],
    }),

    // Get user's own calendars
    getUserCalendars: builder.query({
      query: () => ({
        url: `/api/calendars/my-calendars`,
        method: "GET",
      }),
      providesTags: ["Calendar"],
    }),

    // Get tutor's shared calendars (for students to view availability)
    getTutorCalendars: builder.query({
      query: (tutorId) => ({
        url: `/api/calendars/tutor/${tutorId}`,
        method: "GET",
      }),
      providesTags: (result, error, tutorId) => [
        { type: "Calendar", id: `tutor-${tutorId}` },
      ],
    }),

    // Update calendar
    updateCalendar: builder.mutation({
      query: ({ id, ...body }) => ({
        url: `/api/calendars/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Calendar"],
    }),

    // Delete calendar
    deleteCalendar: builder.mutation({
      query: (id) => ({
        url: `/api/calendars/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Calendar"],
    }),

    // Create event
    createEvent: builder.mutation({
      query: (body) => ({
        url: `/api/events`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["Event", "Calendar"],
    }),

    // Get events by calendar
    getEventsByCalendar: builder.query({
      query: (calendarId) => ({
        url: `/api/events/calendar/${calendarId}`,
        method: "GET",
      }),
      providesTags: (result, error, calendarId) => [
        { type: "Event", id: `calendar-${calendarId}` },
      ],
    }),

    // Update event
    updateEvent: builder.mutation({
      query: ({ id, ...body }) => ({
        url: `/api/events/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Event", "Calendar"],
    }),

    // Delete event
    deleteEvent: builder.mutation({
      query: (id) => ({
        url: `/api/events/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Event", "Calendar"],
    }),
  }),

  overrideExisting: false,
});

export const {
  useCreateCalendarMutation,
  useGetUserCalendarsQuery,
  useGetTutorCalendarsQuery,
  useUpdateCalendarMutation,
  useDeleteCalendarMutation,
  useCreateEventMutation,
  useGetEventsByCalendarQuery,
  useUpdateEventMutation,
  useDeleteEventMutation,
} = calendarApiSlice;

export default calendarApiSlice;
