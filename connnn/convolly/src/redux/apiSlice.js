import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const baseQuery = fetchBaseQuery({
  baseUrl: "https://convolly-backend.onrender.com",
  prepareHeaders: (headers, { getState }) => {
    const state = getState();
    const token = state?.app?.userInfo?.accessToken;

    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }
    return headers;
  },
});

const baseQueryWithReauth = async (args, api, extraOptions) => {
  console.log("API Request:", args, api, extraOptions);

  const result = await baseQuery(args, api, extraOptions);

  // Handle authentication errors
  if (result.error && result.error.status === 401) {
    // Could implement token refresh logic here if needed
    console.log("Authentication error:", result.error);
  }

  return result;
};

export const generalApiSlice = createApi({
  baseQuery: baseQueryWithReauth,
  reducerPath: "api",
  endpoints: (builder) => ({}),
});
