import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const baseQuery = fetchBaseQuery({
  baseUrl: "https://convolly-backend.onrender.com",
  prepareHeaders: (headers, { getState }) => {
    const state = getState();
    const token = state?.app?.userInfo?.accessToken;

    if (token) {
      headers.set("Authorization", `Bearer ${token}`);
    }
    return headers;
  },
});

const baseQueryWithReauth = async (args, api, extraOptions) => {
  const state = api.getState();
  const baseUrl = "https://convolly-backend.onrender.com";

  console.log(args, api, extraOptions);
  const result = await baseQuery(
    { ...args, url: `${baseUrl}${args.url}` },
    api,
    extraOptions
  );

  return result;
};

export const generalApiSlice = createApi({
  baseQuery: baseQueryWithReauth,
  reducerPath: "api",
  endpoints: (builder) => ({}),
});
