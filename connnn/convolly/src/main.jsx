import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./app.jsx";
import { store } from "./redux/store.js";
import { Provider } from "react-redux";
import { appRoutes } from "./_config/inAppUrl.jsx";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import { ToastProvider } from "./context/toastContext/toastContext";

const routerConfig = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    children: appRoutes
  }
]);

const RootApp = () => {
  return <RouterProvider router={routerConfig} />;
};

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <Provider store={store}>
      <ToastProvider>
        <RootApp />
      </ToastProvider>
    </Provider>
  </StrictMode>
);
