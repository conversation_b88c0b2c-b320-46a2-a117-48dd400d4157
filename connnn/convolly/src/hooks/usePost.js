import { useState } from "react";
import { useToast } from "../context/toastContext/toastContext";
import { useDispatch } from "react-redux";
import { logOut } from "@/redux/appSlice";

//General POST handler hook for  Query mutations
const usePost = (mutation) => {
  const [triggerMutation, { isLoading, isError }] = mutation();
  const { showSuccess, showError } = useToast();
  const dispatch = useDispatch()

  const [error, setError] = useState(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const handlePost = async (data) => {
    try {
      const response = await triggerMutation(data).unwrap();
      setIsSuccess(true);
      showSuccess(response?.message);
      return response;
    } catch (err) {
      const message = err?.data?.message || "An error occurred";
      setIsSuccess(false);
      setError(message);
      showError(message);

      if(err?.data?.status == 401) {
        dispatch(logOut())
      }
    }
  };

  return {
    handlePost,
    isLoading,
    isError,
    isSuccess,
    error
  };
};

export default usePost;
