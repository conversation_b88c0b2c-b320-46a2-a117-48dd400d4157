import { useState, useEffect, useMemo } from 'react';
import { useGetTutorCalendarQuery } from '@/redux/slices/student/scheduleApiSlice';
import { 
  getNextAvailableSlots, 
  formatAvailabilityForCard, 
  getAvailabilityStatus,
  isAvailableToday 
} from '@/utils/availabilityUtils';

/**
 * Hook to get availability data for a single tutor
 * @param {string} tutorId - The tutor's ID
 * @param {Object} tutor - The tutor object with timeAvailable data
 * @param {Object} options - Configuration options
 * @returns {Object} Availability data and status
 */
export const useTutorAvailability = (tutorId, tutor, options = {}) => {
  const {
    enabled = true,
    maxSlots = 5,
    daysToCheck = 7,
    compact = false
  } = options;

  // Fetch tutor calendar data
  const { 
    data: calendarData, 
    isLoading, 
    error,
    refetch 
  } = useGetTutorCalendarQuery(tutorId, {
    skip: !enabled || !tutorId || !tutor,
    // Refetch every 5 minutes to keep availability current
    pollingInterval: 5 * 60 * 1000,
  });

  // Calculate availability data
  const availabilityData = useMemo(() => {
    if (!tutor || !calendarData?.data) {
      return {
        hasAvailability: false,
        slots: [],
        status: 'Loading...',
        isAvailableToday: false
      };
    }

    const bookedSlots = calendarData.data.bookedSlots || [];
    
    // Get next available slots
    const nextSlots = getNextAvailableSlots(
      tutor, 
      bookedSlots, 
      daysToCheck, 
      maxSlots
    );

    // Format for display
    const formattedData = formatAvailabilityForCard(nextSlots, compact ? 1 : 3);
    
    // Get status text
    const statusText = getAvailabilityStatus(tutor, bookedSlots);
    
    // Check if available today
    const availableToday = isAvailableToday(tutor, bookedSlots);

    return {
      ...formattedData,
      status: statusText,
      isAvailableToday: availableToday,
      allSlots: nextSlots,
      bookedSlots
    };
  }, [tutor, calendarData, daysToCheck, maxSlots, compact]);

  return {
    ...availabilityData,
    isLoading,
    error,
    refetch
  };
};

/**
 * Hook to manage availability for multiple tutors
 * Optimizes API calls and provides batch availability data
 * @param {Array} tutors - Array of tutor objects
 * @param {Object} options - Configuration options
 * @returns {Object} Availability data for all tutors
 */
export const useMultipleTutorAvailability = (tutors = [], options = {}) => {
  const {
    enabled = true,
    maxTutors = 10, // Limit to prevent too many API calls
    staggerDelay = 100 // Delay between API calls in ms
  } = options;

  const [availabilityMap, setAvailabilityMap] = useState({});
  const [loadingStates, setLoadingStates] = useState({});

  // Limit tutors to prevent overwhelming the API
  const limitedTutors = tutors.slice(0, maxTutors);

  useEffect(() => {
    if (!enabled || limitedTutors.length === 0) return;

    // Stagger API calls to prevent overwhelming the server
    limitedTutors.forEach((tutor, index) => {
      setTimeout(() => {
        setLoadingStates(prev => ({
          ...prev,
          [tutor._id]: true
        }));
      }, index * staggerDelay);
    });
  }, [limitedTutors, enabled, staggerDelay]);

  // Get availability for a specific tutor
  const getTutorAvailability = (tutorId) => {
    return availabilityMap[tutorId] || {
      hasAvailability: false,
      slots: [],
      status: 'Loading...',
      isAvailableToday: false,
      isLoading: loadingStates[tutorId] || false
    };
  };

  // Update availability data when individual tutor data changes
  const updateTutorAvailability = (tutorId, data) => {
    setAvailabilityMap(prev => ({
      ...prev,
      [tutorId]: data
    }));
    setLoadingStates(prev => ({
      ...prev,
      [tutorId]: false
    }));
  };

  return {
    getTutorAvailability,
    updateTutorAvailability,
    availabilityMap,
    loadingStates,
    isLoading: Object.values(loadingStates).some(loading => loading)
  };
};

/**
 * Hook for lightweight availability status (without full calendar data)
 * Uses only the tutor's timeAvailable data for quick status display
 * @param {Object} tutor - Tutor object with timeAvailable
 * @returns {Object} Basic availability status
 */
export const useLightweightAvailability = (tutor) => {
  return useMemo(() => {
    if (!tutor?.timeAvailable || tutor.timeAvailable.length === 0) {
      return {
        hasAvailability: false,
        status: 'No availability set',
        isAvailableToday: false
      };
    }

    // Quick check based on timeAvailable without API call
    const today = new Date().getDay();
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const todayName = dayNames[today];
    
    const todayAvailability = tutor.timeAvailable.find(slot => slot.label === todayName);
    const hasAvailabilityToday = todayAvailability && todayAvailability.from && todayAvailability.to;
    
    // Get next available day
    let nextAvailableDay = null;
    for (let i = 1; i <= 7; i++) {
      const checkDay = (today + i) % 7;
      const checkDayName = dayNames[checkDay];
      const daySlot = tutor.timeAvailable.find(slot => slot.label === checkDayName);
      if (daySlot && daySlot.from && daySlot.to) {
        nextAvailableDay = checkDayName;
        break;
      }
    }

    let status = 'Limited availability';
    if (hasAvailabilityToday) {
      status = 'Available today';
    } else if (nextAvailableDay) {
      if (nextAvailableDay === dayNames[(today + 1) % 7]) {
        status = 'Available tomorrow';
      } else {
        status = `Available ${nextAvailableDay}`;
      }
    }

    return {
      hasAvailability: tutor.timeAvailable.length > 0,
      status,
      isAvailableToday: hasAvailabilityToday,
      nextAvailableDay
    };
  }, [tutor]);
};

export default useTutorAvailability;
