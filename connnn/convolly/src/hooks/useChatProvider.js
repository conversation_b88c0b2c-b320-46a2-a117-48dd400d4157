import { useEffect, useState } from "react";
import useSocket, { useSocketStore } from "./useSocket";
import { useSelector } from "react-redux";
import { safelyBind } from "@/utils/event";
import { serializeToChatMessage } from "@/pages/messaging/utils";
import {
  CHAT_MESSAGE_SAVED,
  CHAT_SEND_MESSAGE,
  ERROR_CORRUPTED_CONNECTION,
  ERROR_UNSUPPORTED_BROWSER_ENVIRONMENT,
} from "@/constants";
import { toast } from "react-toastify";

const useChatProvider = () => {
  const api = useSocket();

  useEffect(() => {
    if (api.socket) {
      api.socket.emit("join-chat-room");
    }
  }, [api.socket]);

  return api;
};

export const useChatHook = (opts) => {
  const { socket, error } = useSocketStore();

  const [loading, setLoading] = useState(false);

  const currentUser = useSelector((state) => state?.app?.userInfo?.user);

  useEffect(() => {
    if (socket) {
      const { onMessageSaved } = opts || {};

      const onSaved = (message, tempId) => {
        setLoading(false);
        onMessageSaved?.(message, tempId);
      };

      safelyBind(socket, CHAT_MESSAGE_SAVED, onSaved);

      return () => {
        socket.off(CHAT_MESSAGE_SAVED, onSaved);
      };
    }
  }, [socket, opts]);

  useEffect(() => {
    if (error) setLoading(false);
  }, [error]);

  const sendChatMessage = async (
    chatOpts = { lookupConversation: false, chat: undefined }
  ) => {
    try {
      if (!socket) {
        toast.error(ERROR_CORRUPTED_CONNECTION);
        return;
      }
      const { chat, ...sendOpts } = chatOpts;

      setLoading(true);

      const message = await serializeToChatMessage(
        currentUser,
        opts?.receiver,
        chat || opts?.chat
      );

      socket.emit(CHAT_SEND_MESSAGE, message, sendOpts);

      return message;
    } catch (err) {
      toast.error(ERROR_UNSUPPORTED_BROWSER_ENVIRONMENT);
    }
  };

  return { socket, currentUser, sendChatMessage, loading };
};

export default useChatProvider;
