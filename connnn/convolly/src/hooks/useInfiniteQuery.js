import { useCallback, useMemo, useRef, useState } from "react";
import { useQuery } from "react-query";

const useInfiniteQuery = (query) => {
  const [data, setData] = useState([]);

  const [error, setError] = useState(false);

  const [refetching, setRefetching] = useState(false);

  const stateRef = useRef({});

  const { fn: queryFn } = useMemo(
    () => ({
      fn: async () => {
        try {
          if (!query.enabled) return;

          setRefetching(!!stateRef.current.pagination);

          const res = await query.queryFn(stateRef.current.pagination);

          stateRef.current.pagination = res.details.pagination;

          console.log(res.data);

          setData((data) =>
            query.backward ? [...res.data, ...data] : [...data, ...res.data]
          );
          return res.details.pagination.hasMore;
        } catch (err) {
          setError(true);

          if (!stateRef.current.pagination) throw err;
          return false;
        } finally {
          setRefetching(false);
        }
      },
    }),
    [query.enabled]
  );

  const api = useQuery({
    queryKey: query.queryKey,
    queryFn,
    enabled: query.enabled,
  });

  const isFetching = stateRef.current.pagination ? false : api.isFetching;

  const isRefetching = api.isRefetching || refetching;

  return {
    ...api,
    data,
    isFetching,
    pagination: stateRef.current.pagination,
    hasMore: stateRef.current.pagination?.hasMore || false,
    isError: api.isError || error,
    isRefetching,
    isPending: isFetching || isRefetching,
    fetchNextPage: useCallback(async () => {
      try {
        return await queryFn();
      } catch (err) {
        if (err) return false;
      }
    }, [queryFn]),
  };
};

export default useInfiniteQuery;
