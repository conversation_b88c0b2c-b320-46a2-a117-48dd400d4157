import React from "react";
import { Card, CardContent } from "../../../../components/card/card";
import { CustomSelect } from "../../../../components/select/select";
import { Button } from "../../../../components/button/button";
import heroBackgroundImage from "../../../../assets/images/heroBackgroundImage.png";
import { useForm } from "react-hook-form";


export const HeroSection = () => {
  const {
    register,
    control,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm();

  return (
    <section
      className="relative w-full h-full py-[50px] sm:px-8 px-3 bg-cover bg-center"
      style={{ backgroundImage: `url(${heroBackgroundImage})` }}
    >
      <div className="flex flex-col items-start gap-12 max-w-[814px]">
        <div className="flex flex-col items-start gap-3 w-full">
          <h1 className="font-bold text-secondary tracking-[0px] lg:leading-[102px] md:leading-[80px] leading-[50px] lg:text-[68px] md:text-[54px] sm:text-[40px] text-[30px]">
            Learn Languages For Your <br />
            Professional Journey
          </h1>

          <p className="font-medium text-gray-600 text-[18px] tracking-[0px] leading-[27px]">
            Connect with tutors who understand your industry and accelerate your
            career with targeted language skills.
          </p>
        </div>

        <Card className="w-full shadow-[0px_4px_20px_0px_rgba(26,26,64,0.10)] bg-white">
          <CardContent className="flex flex-col items-end gap-8 p-7">
            <div className="sm:flex items-center gap-5 w-full">
              <div className="flex flex-col items-start gap-3 flex-1 max-sm:mb-5">
                <CustomSelect
                  placeholder="Select language"
                  label="Target Language"
                  options={[
                    { value: "en", label: "English" },
                    { value: "fr", label: "French" },
                    { value: "es", label: "Spanish" }
                  ]}
                  name="language"
                  control={control}
                  isRequired={true}
                  error={errors?.country?.message}
                />
              </div>

              <div className="flex flex-col items-start gap-3 flex-1 max-sm:mb-5">
                <CustomSelect
                  placeholder="Select Industry"
                  label="Industry"
                  options={[
                    { value: "healthcare", label: "Healthcare" },
                    { value: "banking", label: "Banking" },
                    { value: "legal", label: "Legal" },
                    { value: "marketing", label: "Marketing" }
                  ]}
                  name="industry"
                  control={control}
                  isRequired={true}
                  error={errors?.country?.message}
                />
              </div>

              <div className="flex flex-col items-start gap-3 flex-1">
                <CustomSelect
                  placeholder="Select Level"
                  label="Experience Level"
                  options={[
                    { value: "learner", label: "Learner" },
                    { value: "immediate", label: "Immediate" },
                    { value: "fluent", label: "Fluent" }
                  ]}
                  name="level"
                  control={control}
                  isRequired={true}
                  error={errors?.country?.message}
                />
              </div>
            </div>

            <Button className="w-full h-[50px]">Find A Tutor</Button>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};
