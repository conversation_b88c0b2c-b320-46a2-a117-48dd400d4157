import Navbar from "@/components/navbar/navbar";
import React from "react";
import Subcriptions from "./components/Subcriptions";
import MainCard from "./components/MainCard";
import UpNextCard from "./components/UpNextCard";
import { useNavigate } from "react-router-dom";

const LessonTrial = () => {
	const navigate = useNavigate();

	const redirect = () => {
		navigate("/find-tutors");
	};
	return (
		<div className="py-6 w-auto mx-4">
			<div className="gap-[133px] top-[16px]">
				<Navbar />
				<div className="mt-8 flex flex-col items-center justify-center ">
					<div className="gap-10 h-[1282px]">
						<h1 className="w-full text-xl sm:text-3xl text-[#1A1A40] font-bold mb-4">
							Let’s get you ready for your trail lesson
						</h1>
						<div className="shadow-md border rounded-md p-2 sm:p-[30px]">
							<MainCard />
						</div>
						<br />
						<div className="h-auto border shadow-md rounded-md p-2 py-4 sm:p-[30px]">
							<div className="flex justify-between">
								<h1 className="text-2xl font-bold text-[#1A1A40]">Up next</h1>
								<p className="text-sm sm:text-lg"> see more</p>
							</div>{" "}
							<br />
							<div className="py-2">
								<UpNextCard />
								<UpNextCard />
							</div>
						</div>

						<br />
						<div className="h-[320px] w-full">
							<h1 className="text-2xl font-bold pb-4">Subcriptions</h1>
							<div className="flex w-full justify-center gap-4 pb-2">
								<Subcriptions />
								<Subcriptions />
								<Subcriptions />
								<Subcriptions />
								<Subcriptions />
								<Subcriptions />
							</div>
							<div className="">
								<p className="text-xl py-2 font-bold">
									Want to find another tutor?
								</p>
								<p>
									Try different teaching styles to choose your perfect tutor
									match
								</p>
								<br />
								<button
									onClick={redirect}
									className="w-full text-[#333333] text-lg font-bold rounded-md py-2 border bg-white"
								>
									Find another tutor
								</button>
							</div>
						</div>
						<br />
					</div>
				</div>
			</div>
		</div>
	);
};

export default LessonTrial;
