import React, { useState } from "react";
import { Button } from "@/components/button/button";
import paypal from "../../../assets/svgs/payments/paypal-logo.svg";
import visa from "../../../assets/svgs/payments/Visa-logo.svg";
import mastercard from "../../../assets/svgs/payments/mastercard.svg";

const PaymentMethods = ({ onCompletePayment }) => {
	const [selectedMethod, setSelectedMethod] = useState("credit");

	const RadioButton = ({ checked }) => (
		<div
			className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all ${
				checked ? "border-blue-500 bg-blue-500" : "border-gray-300 bg-white"
			}`}
		>
			{checked && <div className="w-2 h-2 rounded-full bg-white"></div>}
		</div>
	);

	return (
		<div className="w-full mx-auto">
			<h2 className="font-bold text-lg text-[#1A1A40] pb-8 capitalize">
				payment method
			</h2>

			<div className="space-y-4">
				{/* Credit Card Option */}
				<div className="flex">
					<button
						onClick={() => setSelectedMethod("credit")}
						className={`w-full flex items-center p-4 border rounded-lg transition-all ${
							selectedMethod === "credit"
								? "border-blue-500 bg-blue-50"
								: "border-gray-200 hover:border-gray-300 bg-white"
						}`}
					>
						<RadioButton checked={selectedMethod === "credit"} />

						<div className="flex-1 flex items-center justify-between ml-3">
							<h3 className="font-medium text-[#1A1A40]">
								Credit card or debit card
							</h3>
							<div className="flex gap-2">
								<img src={visa} alt="visaicon" />
								<img src={mastercard} alt="mastercard icon" />
							</div>
						</div>
					</button>
				</div>

				{/* PayPal Option */}
				<div className="flex">
					<button
						onClick={() => setSelectedMethod("paypal")}
						className={`w-full flex items-center p-4 border rounded-lg transition-all ${
							selectedMethod === "paypal"
								? "border-blue-500 bg-blue-50"
								: "border-gray-200 hover:border-gray-300 bg-white"
						}`}
					>
						<RadioButton checked={selectedMethod === "paypal"} />

						<div className="flex-1 flex items-center justify-between ml-3">
							<h3 className="font-medium text-[#1A1A40]">PayPal</h3>
							<img src={paypal} alt="paypal icon" />
						</div>
					</button>
				</div>

				{/* Complete Payment Section */}
				<div className="pt-6">
					<button
						onClick={onCompletePayment}
						className="bg-primary p-3 rounded-md text-white w-full text-lg font-bold mb-4"
					>
						Complete payment
					</button>

					<p className="text-md text-[#1A1A40] text-center leading-relaxed">
						By pressing the "Complete payment" button, you agree to{" "}
						<a href="#" className="">
							Convolly's Refund and Payment Policy
						</a>
					</p>
				</div>
			</div>
		</div>
	);
};

export default PaymentMethods;
