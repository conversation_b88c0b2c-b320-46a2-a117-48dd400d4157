import { Check } from "lucide-react";
import { But<PERSON> } from "@/components/button/button";
import tutor from "../../../assets/images/tutor1.png"; // Adjust the path as necessary

const SkipModal = ({ isOpen, onClose, onSkip, onContinue }) => {
	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 z-50 flex items-center justify-center">
			{/* Overlay */}
			<div
				className="absolute inset-0 bg-black bg-opacity-30"
				onClick={onClose}
			></div>

			{/* Modal Content - now properly centered */}
			<div className="relative w-[513px] h-[444px] bg-white rounded-lg shadow-lg p-6 mx-4">
				{/* Success Icon */}
				<div className="flex justify-center h-[78px] my-4 mb-6">
					<img
						src={tutor}
						alt="tutor image"
						className="h-[120px] w-[120px] rounded-md"
					/>
				</div>
				<br />
				<div className="pt-6">
					{/* Success Message */}
					<h2 className="text-2xl font-bold text-[#1A1A40] mb-2 text-center">
						Are you sure you want to skip?
					</h2>
					<p className="text-gray-600 mb-8 text-center">
						Any details you share can help John better prepare a great lesson
						for you
					</p>
					<div>
						<button
							className="w-full bg-primary h-[50px] text-white py-2 rounded-lg hover:bg-primary-dark transition-colors"
							onClick={onContinue || onClose}
							disabled={false}
						>
							Back to questions
						</button>
						<button
							className="w-full border h-[50px] border-gray-300 text-gray-700 mt-4 py-2 rounded-lg hover:bg-gray-100 transition-colors"
							onClick={onSkip || onClose}
							disabled={false}
						>
							Skip
						</button>
					</div>
				</div>
			</div>
		</div>
	);
};

export default SkipModal;
