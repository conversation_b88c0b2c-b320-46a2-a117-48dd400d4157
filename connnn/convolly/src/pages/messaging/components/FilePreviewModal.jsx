import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>alogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { cn } from "@/utils";
import { X } from "lucide-react";
import { useEffect, useState } from "react";

const FilePreviewModal = ({ onClose, filePreview }) => {
  const [url, setUrl] = useState("");

  useEffect(() => {
    if (filePreview) {
      const url = URL.createObjectURL(filePreview.file);

      setUrl(url);

      return () => URL.revokeObjectURL(url);
    }
  }, [filePreview]);

  if (!filePreview) return null;

  const maxW = "w-full max-w-[90vw] sm:max-w-lg";

  return (
    <Dialog open={!!filePreview} onOpenChange={onClose}>
      <DialogContent
        className={cn(
          `
      p-0 overflow-hidden
      `,
          maxW
        )}
      >
        <div className="relative">
          <DialogClose asChild>
            <button
              className="absolute top-2 right-2 rounded-full bg-black/70 text-white hover:bg-black/90 p-1"
              onClick={onClose}
            >
              <X className="w-5 h-5" />
            </button>
          </DialogClose>

          <img
            src={url}
            alt={""}
            className="w-full h-auto max-h-[80vh] object-contain"
          />

          {filePreview.name ? (
            <div
              className={cn(
                `
             break-words px-4 py-2 bg-white 
            border-t text-center text-sm text-gray-700
            `,
                maxW
              )}
            >
              {filePreview.name}
            </div>
          ) : null}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FilePreviewModal;
