import React from "react";
import tutor from "@/assets/images/tutorProfileImage.png";
import students from "@/assets/svgs/students.svg";
import fullStar from "@/assets/svgs/fullStar.svg";
import usa from "@/assets/svgs/usa.svg";
import lessons from "@/assets/svgs/lessons.svg";
import favorite from "@/assets/svgs/favorite.svg";
import greenCheck from "@/assets/svgs/greencheck.svg";
import { Button } from "@/components/button/button";

const TutorCard = ({ tutor }) => {
  const {
    image = "", // fallback to static if no dynamic image
    name = "Tutor Name",
    about = "No description available",
    isVerified = false,
    country = "USA",
    // countryFlag = usa,
    specialties = [],
    studentsCount = 0,
    lessonsCount = 0,
    languages = [],
    rating = 0,
    reviewsCount = 0,
    basePrice = 0,
    priceDuration = "per hour",
    isFavorite = false,

    headline = "",
    firstname = "",
    lastname = "",
    teachingSubjects = [],
    motivatePotentialStudent = "",
    teachingExperience = "",
    reviews = "",
    timeAvailable = "",
    aboutMe = about
  } = tutor || {};
  
  return (
    <div className="border border-[#E8E8E8] rounded-xl p-3 flex gap-5 mb-5">
      <div className="w-full">
        <div className="md:flex hidden gap-5 items-stretch">
          <img
            src={image}
            alt="tutor image"
            className="w-full max-w-[290px] max-h-[200px] object-cover rounded-xl"
          />

          <div className="flex w-full gap-5">
            <div className="">
              <div className="flex gap-2 items-center mb-2">
                <h3 className="text-[26px] font-bold">
                  {firstname} {lastname}
                </h3>
                <img src={greenCheck} alt="verified icon" />
                <img src={usa} alt="country icon" />
              </div>

              {/* subjects  */}
              {teachingSubjects.map((subject) => (
                <div key={subject.id} className="flex flex-wrap mb-3">
                  {subject.qualities.map((q, index) => (
                    <span
                      key={index}
                      className="border border-[#E8E8E8] p-2 rounded-full m-1 text-sm"
                    >
                      {q}
                    </span>
                  ))}
                </div>
              ))}

              <div className="flex items-center gap-1 mb-2">
                <img src={students} alt="students icon" />
                <span className="text-[#4B5563] mr-5">300 students</span>

                <img src={lessons} alt="lessons icon" />
                <span className="text-[#4B5563] mr-5">400 lessons</span>
              </div>

              {/* language level  */}
              {languages.map((lang) => (
                <div key={lang.id} className="flex items-center gap-1">
                  <span className="text-[#4B5563]">{lang.name}: </span>
                  <span className="text-black mr-5">{lang.level}</span>
                </div>
              ))}
            </div>

            <div className="flex gap-5 lg:min-w-[45%] justify-end">
              <div className="flex flex-col">
                <div className="flex gap-2 items-center">
                  <img src={fullStar} alt="star icon" className="w-6 h-6" />
                  <h2 className="font-bold text-2xl">4.5</h2>
                </div>
                <p className="text-[#4B5563] shrink-0">60 reviews</p>
              </div>

              <div className="flex flex-col">
                <h2 className="font-bold text-2xl">US${basePrice}</h2>
                <p className="text-[#4B5563] shrink-0">{}</p>
              </div>

              <img src={favorite} alt="star icon" className="w-6 h-6" />
            </div>
          </div>
        </div>

        {/* mobile view */}
        <div className="md:hidden">
          <div className="flex gap-3 mb-3">
            <img
              src={image}
              alt="tutor image"
              className="w-full max-w-[100px] max-h-[120px] object-cover rounded-lg"
            />

            <div className="w-full">
              <div className="flex items-center mb-2">
                <h3 className="text-lg font-bold">
                  {firstname} {lastname}
                </h3>
                <img src={greenCheck} alt="verified icon" className="w-5 h-5" />
                <img src={usa} alt="country icon" className="w-5 h-5" />
                <img
                  src={favorite}
                  alt="favorite icon"
                  className="w-5 h-5 ml-auto"
                />
              </div>

              <div className="flex gap-3">
                <div className="flex flex-col">
                  <div className="flex gap-2 items-center">
                    <img src={fullStar} alt="star icon" className="w-5 h-5" />
                    <h2 className="font-bold text-lg">4.5</h2>
                  </div>
                  <p className="text-[#4B5563] shrink-0">{reviews}</p>
                </div>

                <div className="flex flex-col">
                  <h2 className="font-bold text-lg">US${basePrice}</h2>
                  <p className="text-[#4B5563] shrink-0">50-min lesson</p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-wrap mb-3">
            {teachingSubjects.map((subject) => (
              <div key={subject.id} className="flex flex-wrap mb-3">
                {subject.qualities.map((q, index) => (
                  <span
                    key={index}
                    className="border border-[#E8E8E8] p-2 rounded-full m-1 text-sm"
                  >
                    {q}
                  </span>
                ))}
              </div>
            ))}
          </div>

          <div className="flex items-center gap-1 mb-2">
            <img src={students} alt="students icon" />
            <span className="text-[#4B5563] mr-5">300 students</span>

            <img src={lessons} alt="lessons icon" />
            <span className="text-[#4B5563] mr-5">400 lessons</span>
          </div>

          <div className="flex items-center gap-1">
            {languages.map((lang) => (
              <div key={lang.id}>
                <span className="text-[#4B5563]">{lang.name}: </span>
                <span className="text-black">{lang.level}</span>
              </div>
            ))}
          </div>
        </div>

        <h3 className="sm:text-xl text-secondary font-bold mb-3 mt-5">About</h3>

        <p className="text-[#4B5563] sm:text-lg mb-3">{aboutMe}</p>

        <a href="#" className="underline block mb-5 text-primary font-bold">
          Read more
        </a>

        <Button className="w-full sm:hidden">Book free lesson</Button>
      </div>

      <div className="lg:max-w-[240px] grow hidden lg:flex flex-col gap-4">
        <img
          src={image}
          alt="tutor image"
          className="w-full h-full object-cover rounded-xl"
        />

        <Button className="w-full text-lg">Book free lesson</Button>
      </div>
    </div>
  );
};

export default TutorCard;
