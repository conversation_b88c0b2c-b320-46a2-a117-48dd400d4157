import React, { useState } from "react";
import { format, addDays, startOfWeek, addWeeks } from "date-fns";
import rightArrow from "@/assets/svgs/rightArrow.svg";
import leftArrow from "@/assets/svgs/leftArrow.svg";
import tutorImage from "@/assets/images/tutor1.png";
import { Button } from "@/components/button/button";
import { useNavigate } from "react-router-dom";

const DAYS = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

// Mock event generator
const generateMockEvents = (weekOffset = 0) => {
  const navigate = useNavigate()

  const base = startOfWeek(new Date(), { weekStartsOn: 0 });
  const start = addWeeks(base, weekOffset);
  return [
    { day: 1, time: 7, label: "7:00–7:30AM\nWake-Up" },
    { day: 2, time: 7, label: "7:00–7:30AM\nWake-Up" },
    { day: 3, time: 11, label: "11:00–11:30AM\nMeeting" },
    { day: 4, time: 7, label: "7:00–7:30AM\nWorkout" },
    { day: 5, time: 13, label: "1:00–1:30PM\nLunch" },
    { day: 6, time: 7, label: "7:00–7:30AM\nRun" },
    { day: 0, time: 10, label: "10:00–10:30AM\nCoffee" },
    { day: 1, time: 13, label: "1:00–1:30PM\nCall" },
    { day: 2, time: 17, label: "5:00–5:30PM\nReview" },
    { day: 3, time: 19, label: "7:00–7:30PM\nDinner" },
    { day: 5, time: 22, label: "10:00–10:30PM\nWind Down" },
    { day: 0, time: 23, label: "11:00–11:30PM\nRead" }
  ];
};

export default function Schedule() {
  const [hoveredSlot, setHoveredSlot] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [weekOffset, setWeekOffset] = useState(0);
  const [slotDuration, setSlotDuration] = useState(25);

  const HOURS = Array.from({ length: 17 }, (_, i) => 7 + i);
  const SLOTS_PER_HOUR = 60 / slotDuration;
  const slotHeight = slotDuration === 25 ? 35 : 70;

  const startDate = addWeeks(
    startOfWeek(new Date(), { weekStartsOn: 0 }),
    weekOffset
  );
  const endDate = addDays(startDate, 6);
  const events = generateMockEvents(weekOffset);

  const formatRange = `${format(startDate, "MMM d")} – ${format(
    endDate,
    "d, yyyy"
  )}`;
  const timeOptions = [25, 50];

  // When hovering, we store { key, dayIdx, hour, slotIdx }
  const handleMouseEnter = (dayIdx, hour, slotIdx) => {
    const key = `${dayIdx}-${hour}-${slotIdx}`;
    setHoveredSlot({ key, dayIdx, hour, slotIdx });
  };

  const handleMouseLeave = () => setHoveredSlot(null);

  // Compute actual date+time range for a hovered slot
  const getHoverDateTime = () => {
    if (!hoveredSlot) return {};
    const { dayIdx, hour, slotIdx } = hoveredSlot;
    const date = addDays(startDate, dayIdx);

    // Start time
    const startTime = new Date(date);
    startTime.setHours(hour, slotIdx * slotDuration);

    // End time
    const endTime = new Date(startTime);
    endTime.setMinutes(endTime.getMinutes() + slotDuration);

    return {
      dateLabel: format(date, "EEEE, MMM d, yyyy"),
      timeLabel: `${format(startTime, "h:mm a")} – ${format(endTime, "h:mm a")}`
    };
  };

  const { dateLabel, timeLabel } = getHoverDateTime();

  return (
    <div className="w-full">
      {/* Duration Toggle */}
      <div className="flex items-center bg-[#EBEDF0] p-2 rounded-lg mb-7">
        {timeOptions.map((opt) => (
          <button
            key={opt}
            className={`px-4 w-full font-bold sm:text-xl text-lg py-1 transition-all rounded-lg ${
              slotDuration === opt ? "bg-white text-secondary" : ""
            }`}
            onClick={() => setSlotDuration(opt)}
          >
            {opt} mins
          </button>
        ))}
      </div>

      {/* Week Nav */}
      <div className="flex justify-between items-center mb-7">
        <button
          onClick={() => setWeekOffset((w) => w - 1)}
          className="bg-[#EBEDF0] p-2 rounded-lg"
        >
          <img src={leftArrow} alt="prev" />
        </button>
        <div className="text-center">
          <p className="sm:text-xl text-lg font-bold">{formatRange}</p>
          <p className="text-base text-[#4B5563] mt-2">
            Nigeria · {format(new Date(), "H:mm")} (GMT+02:00)
          </p>
        </div>
        <button
          onClick={() => setWeekOffset((w) => w + 1)}
          className="bg-[#EBEDF0] p-2 rounded-lg"
        >
          <img src={rightArrow} alt="next" />
        </button>
      </div>

      {/* Header */}
      <div className="grid grid-cols-8 text-sm font-medium border-b">
        <div></div>
        {DAYS.map((_, i) => (
          <div key={i} className="pl-2 py-2 border-l">
            <p className="text-[#71717A]">
              {format(addDays(startDate, i), "EEE")}
            </p>
            <p className="text-black">{format(addDays(startDate, i), "dd")}</p>
          </div>
        ))}
      </div>

      {/* Body */}
      <div className="grid grid-cols-8">
        {/* Time Labels */}
        <div className="flex flex-col">
          {HOURS.flatMap((hour) =>
            Array.from({ length: SLOTS_PER_HOUR }).map((_, slotIdx) => (
              <div
                key={`${hour}-${slotIdx}`}
                className="text-xs text-gray-600 flex items-start"
                style={{ height: slotHeight }}
              >
                {slotIdx === 0 &&
                  (hour < 12
                    ? `${hour} AM`
                    : hour === 12
                    ? "12 PM"
                    : `${hour - 12} PM`)}
              </div>
            ))
          )}
        </div>

        {/* Slots */}
        {DAYS.map((_, dayIdx) => (
          <div key={dayIdx} className="flex flex-col border-l relative">
            {HOURS.flatMap((hour) =>
              Array.from({ length: SLOTS_PER_HOUR }).map((_, slotIdx) => {
                const key = `${dayIdx}-${hour}-${slotIdx}`;
                const isHovering = hoveredSlot?.key === key;
                const event = events.find(
                  (e) => e.day === dayIdx && e.time === hour
                );

                return (
                  <div
                    key={key}
                    className={`relative border-b h-[1px] ${
                      event && slotIdx === 0
                        ? "bg-[#0EA5E91A] border-l-2 border-l-[#0EA5E9]"
                        : ""
                    }`}
                    style={{ height: slotHeight }}
                    onMouseEnter={() => handleMouseEnter(dayIdx, hour, slotIdx)}
                    onMouseLeave={handleMouseLeave}
                    onMouseMove={(e) =>
                      setMousePosition({ x: e.clientX + 10, y: e.clientY + 10 })
                    }
                    onClick={() => {
                      if (!event) {
                        navigate("/payment");
                      }
                    }}
                  >
                    {/* Existing event */}
                    {event && slotIdx === 0 && (
                      <div className="absolute inset-1 bg-sky-100 text-xs text-[#0369A1] rounded whitespace-pre-line z-10">
                        {event.label}
                      </div>
                    )}

                    {/* Hover popup */}
                    {!event && isHovering && (
                      <div
                        className="fixed z-50 bg-white text-xs p-5 border rounded-xl shadow w-full max-w-[350px]"
                        style={{
                          left: mousePosition.x,
                          top: mousePosition.y
                        }}

                      >
                        <div className="flex gap-2 items-center mb-3">
                          <img
                            src={tutorImage}
                            alt="tutor"
                            className="w-[55px] h-[55px] rounded-full object-cover"
                          />

                          <h4 className="sm:text-xl text-secondary font-bold">
                            Steve Gilbert D.
                          </h4>
                        </div>

                        <p className="text-secondary sm:text-lg mb-1">
                          Date: {dateLabel}
                        </p>

                        <p className="text-secondary sm:text-lg mb-2">
                          Time: {timeLabel}
                        </p>

                        <p className="text-secondary sm:text-lg mb-5">
                          Price: -
                        </p>

                        <Button className="h-[50px] w-full">Book free lession</Button>
                      </div>
                    )}
                  </div>
                );
              })
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
