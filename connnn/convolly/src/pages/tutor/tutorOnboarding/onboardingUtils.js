export const industrySpecializationOptions = [
	{
		value: "Business & Corporate English",
		label: "Business & Corporate English",
	},
	{
		value: "Medical & Healthcare English",
		label: "Medical & Healthcare English",
	},
	{
		value: "Legal & Compliance English",
		label: "Legal & Compliance English",
	},
	{
		value: "English for Tech & IT Professionals",
		label: "English for Tech & IT Professionals",
	},
	{
		value: "English for Finance, Accounting & Banking",
		label: "English for Finance, Accounting & Banking",
	},
	{ value: "Aviation English", label: "Aviation English" },
	{
		value: "English for Tourism & Hospitality",
		label: "English for Tourism & Hospitality",
	},
	{
		value: "English for Academic & Research Purposes",
		label: "English for Academic & Research Purposes",
	},
	{
		value: "English for Skilled Trades",
		label: "English for Skilled Trades",
	},
	{
		value: "English for Logistics, Transport & Supply Chain",
		label: "English for Logistics, Transport & Supply Chain",
	},
	{
		value: "English for Creative Professionals",
		label: "English for Creative Professionals",
	},
	{
		value: "English for Media & Communication",
		label: "English for Media & Communication",
	},
	{
		value: "English for International Relations & NGOs",
		label: "English for International Relations & NGOs",
	},
	{
		value: "English for Government & Public Sector",
		label: "English for Government & Public Sector",
	},
	{
		value: "English for Teachers & Educators",
		label: "English for Teachers & Educators",
	},
	{
		value: "English for Performing Arts & Entertainment",
		label: "English for Performing Arts & Entertainment",
	},
	{
		value: "English for Digital & Remote Work",
		label: "English for Digital & Remote Work",
	},
	{
		value: "English for Real Estate & Property Professionals",
		label: "English for Real Estate & Property Professionals",
	},
	{
		value: "English for Culinary & Food Industry",
		label: "English for Culinary & Food Industry",
	},
];

export const levelOptions = [
	{ value: "A1", label: "Beginners (CEFR-level A1)" },
	{ value: "A2", label: "Pre-intermediate (CEFR-level A2)" },
	{ value: "B1", label: "Intermediate (CEFR-level-B1)" },
	{ value: "B2", label: "Upper-intermediate (CEFR-level-B2)" },
	{ value: "C1", label: "Advanced (CEFR-level- C1)" },
	{ value: "C2", label: "Proficiency (CEFR-level-C2)" },
	{ value: "Native", label: "Native" },
];

export const experienceOptions = [
	{ value: "A1", label: "Beginners (CEFR-level A1)" },
	{ value: "A2", label: "Pre-intermediate (CEFR-level A2)" },
	{ value: "B1", label: "Intermediate (CEFR-level-B1)" },
	{ value: "B2", label: "Upper-intermediate (CEFR-level-B2)" },
	{ value: "C1", label: "Advanced (CEFR-level- C1)" },
	{ value: "C2", label: "Proficiency (CEFR-level-C2)" },
	{ value: "Native", label: "Native" },
];

export const qualitiesOptions = [
	{ label: "Excellent materials", value: "Excellent materials" },
	{ label: "Tech savvy", value: "Tech savvy" },
	{ label: "Motivation guru", value: "Motivation guru" },
	{ label: "Great conversation", value: "Great conversation" },
	{ label: "Fun", value: "Fun" },
	{ label: "Great for beginners", value: "Great for beginners" },
	{
		label: "Great for intermediate",
		value: "Great for intermediate",
	},
	{ label: "Great for advanced", value: "Great for advanced" },
	{ label: "Cultural insights", value: "Cultural insights" },
];

export const specialititesOptions = [
	{ label: "IELTS General", value: "IELTS" },
	{ label: "Academic english", value: "Academic english" },
	{ label: "Business english", value: "Business english" },
	{ label: "Interview preparation", value: "Interview preparation" },
	{ label: "English literature", value: "English literature" },
	{ label: "Accent reduction", value: "Accent reduction" },
	{ label: "Grammar development", value: "Grammar development" },
	{
		label: "Listening comprehension",
		value: "Listening comprehension",
	},
	{ label: "Phonetics", value: "Phonetics" },
	{ label: "Reading comprehension", value: "Reading comprehension" },
	{ label: "Speaking practice", value: "Speaking practice" },
	{
		label: "Vocabulary development",
		value: "Vocabulary development",
	},
	{ label: "Writing correction", value: "Writing correction" },
];
