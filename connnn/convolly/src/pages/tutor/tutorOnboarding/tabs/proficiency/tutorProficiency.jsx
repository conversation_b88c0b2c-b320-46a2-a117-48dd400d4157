import React, { useEffect } from "react";
import { But<PERSON> } from "@/components/button/button";
import { CustomSelect } from "@/components/select/select";
import MultiSelect from "@/components/select/multiSelect";
import { useForm } from "react-hook-form";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { useSelector } from "react-redux";

const TutorProficiency = ({ setActiveTab, instructorDetails }) => {
  const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);

  const {
    register,
    control,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm();

  const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
    useUpdateProfileMutation
  );

  const updateProficiency = async (data) => {
    const res = await handleUpdateTutor({
      // get the title of the subject submitted in the first page and bundle it into teachingSubjects
      teachingSubjects: [
        { title: instructorDetails?.teachingSubjects[0]?.title, ...data }
      ],
      userId: tutorId,
      role: "tutor"
    });

    if (res) {
      setActiveTab("Certification");
    }
  };

  useEffect(() => {
    const subject = instructorDetails?.teachingSubjects?.[0];

    if (subject) {
      setValue("experienceLevel", subject.experienceLevel);
      setValue("qualities", subject.qualities);
      setValue("specialities", subject.specialities);
    }
  }, [instructorDetails, setValue]);

  return (
    <form
      onSubmit={handleSubmit(updateProficiency)}
      className="max-w-[528px] w-[93%] mx-auto"
    >
      <h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
        Proficiency
      </h2>
      <p className="text-[#4B5563] sm:text-lg mb-8">
        Select your English proficiency level to personalize your teaching
        experience. You can select more than one options in each category
      </p>

      <CustomSelect
        placeholder="Select your experience level"
        label="Experience level"
        options={[
          { value: "A1", label: "Beginners (CEFR-level A1)" },
          { value: "A2", label: "Pre-intermediate (CEFR-level A2)" },
          { value: "B1", label: "Intermediate (CEFR-level-B1)" },
          { value: "B2", label: "Upper-intermediate (CEFR-level-B2)" },
          { value: "C1", label: "Advanced (CEFR-level- C1)" },
          { value: "C2", label: "Proficiency (CEFR-level-C2)" }
        ]}
        className="p-5 py-[22px]"
        parentClassName="mb-7"
        name="experienceLevel"
        control={control}
        isRequired={true}
        error={errors?.experienceLevel?.message}
      />

      <MultiSelect
        options={[
          { label: "Excellent materials", value: "Excellent materials" },
          { label: "Tech savvy", value: "Tech savvy" },
          { label: "Motivation guru", value: "Motivation guru" },
          { label: "Great conversation", value: "Great conversation" },
          { label: "Fun", value: "Fun" },
          { label: "Great for beginners", value: "Great for beginners" },
          { label: "Great for intermediate", value: "Great for intermediate" },
          { label: "Great for advanced", value: "Great for advanced" },
          { label: "Cultural insights", value: "Cultural insights" }
        ]}
        placeholder="Select your qualities"
        label="Qualities"
        control={control}
        name="qualities"
        parentClassName="mb-7"
      />

      <MultiSelect
        options={[
          { label: "GMAT", value: "GMAT" },
          { label: "IELTS", value: "IELTS" },
          { label: "SAT", value: "SAT" },
          { label: "TOEFL", value: "TOEFL" },
          { label: "TOEIC", value: "TOEIC" },
          { label: "Academic english", value: "Academic english" },
          { label: "Business english", value: "Business english" },
          { label: "Interview preparation", value: "Interview preparation" },
          { label: "English literature", value: "English literature" },
          { label: "Accent reduction", value: "Accent reduction" },
          { label: "Grammar development", value: "Grammar development" },
          {
            label: "Listening comprehension",
            value: "Listening comprehension"
          },
          { label: "Phonetics", value: "Phonetics" },
          { label: "Reading comprehension", value: "Reading comprehension" },
          { label: "Speaking practice", value: "Speaking practice" },
          { label: "Vocabulary development", value: "Vocabulary development" },
          { label: "Writing correction", value: "Writing correction" }
        ]}
        placeholder="Select your specialities"
        label="Specialities"
        control={control}
        name="specialities"
        parentClassName="mb-10"
      />

      <div className="sm:flex gap-5">
        <Button
          className="w-full h-[50px] bg-white border-[#D2D2D2] text-secondary border hover:bg-white"
          onClick={() => setActiveTab("Photo")}
        >
          Back
        </Button>

        <Button className="w-full h-[50px]" disabled={updating} type="submit">
          Save And Continue
        </Button>
      </div>
    </form>
  );
};

export default TutorProficiency;
