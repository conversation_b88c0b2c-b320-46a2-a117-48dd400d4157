import React, { useEffect, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import InputField from "@/components/inputs";
import { CustomSelect } from "@/components/select/select";
import { Trash2 } from "lucide-react";
import PhoneInputWithCountry from "@/components/inputs/phoneInputWithCountry";
import { Button } from "@/components/button/button";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { useSelector } from "react-redux";
import Loader from "@/components/loader/loader";
import {
	industrySpecializationOptions,
	levelOptions,
} from "../../onboardingUtils";

const AboutTutor = ({ setActiveTab, instructorDetails }) => {
	const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);
	const [countries, setCountries] = useState([]);
	const [languages, setLanguages] = useState([]);
	const [loadingCountries, setLoadingCountries] = useState(false);
	const [loadingLanguages, setLoadingLanguages] = useState(false);

	const {
		register,
		control,
		handleSubmit,
		setValue,
		watch,
		formState: { errors },
	} = useForm({
		defaultValues: {
			languages: [{ name: "", level: "Native" }],
			teachingSubjects: [{ title: "" }],
		},
	});

	const { fields, append, remove } = useFieldArray({
		control,
		name: "languages",
	});

	const handleAddLanguage = () => {
		if (fields.length < 10) {
			// Optional: Limit to 10 languages
			append({ name: "", level: "" });
		}
	};

	const { fields: teachingFields } = useFieldArray({
		control,
		name: "teachingSubjects",
	});

	const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
		useUpdateProfileMutation
	);

	const updateAbout = async (data) => {
		const res = await handleUpdateTutor({
			...data,
			userId: tutorId,
			role: "tutor",
		});

		if (res) {
			setActiveTab("Photo");
		}
	};

	// Fetch countries from REST Countries API
	useEffect(() => {
		const fetchCountries = async () => {
			try {
				setLoadingCountries(true);
				const response = await fetch(
					"https://restcountries.com/v3.1/all?fields=name"
				);
				if (!response.ok) {
					throw new Error(`HTTP error! Status: ${response.status}`);
				}
				const data = await response.json();
				const countryOptions = data
					.map((country) => ({
						value: country.name.common,
						label: country.name.common,
					}))
					.sort((a, b) => a.label.localeCompare(b.label));
				setCountries(countryOptions);
			} catch (error) {
				console.error("Error fetching countries:", error.message);
				setCountries([{ value: "", label: "Unable to load countries" }]);
			} finally {
				setLoadingCountries(false);
			}
		};

		fetchCountries();
	}, []);

	// Fetch languages from REST Countries API
	useEffect(() => {
		const fetchLanguages = async () => {
			try {
				setLoadingLanguages(true);
				const response = await fetch(
					"https://restcountries.com/v3.1/all?fields=languages"
				);
				if (!response.ok) {
					throw new Error(`HTTP error! Status: ${response.status}`);
				}
				const data = await response.json();
				const languageSet = new Set();
				data.forEach((country) => {
					if (country.languages) {
						Object.values(country.languages).forEach((lang) =>
							languageSet.add(lang)
						);
					}
				});
				const languageOptions = Array.from(languageSet)
					.map((lang) => ({
						value: lang,
						label: lang,
					}))
					.sort((a, b) => a.label.localeCompare(b.label));
				setLanguages(languageOptions);
			} catch (error) {
				console.error("Error fetching languages:", error.message);
				setLanguages([{ value: "", label: "Unable to load languages" }]);
			} finally {
				setLoadingLanguages(false);
			}
		};

		fetchLanguages();
	}, []);

	useEffect(() => {
		if (instructorDetails) {
			setValue("firstname", instructorDetails?.firstname);
			setValue("lastname", instructorDetails?.lastname);
			setValue("email", instructorDetails?.email);
			setValue("countryOfBirth", instructorDetails?.countryOfBirth);
			setValue("phoneno", instructorDetails?.phoneno);
			setValue(
				"industrySpecialization",
				instructorDetails?.industrySpecialization
			);

			setValue(
				"teachingSubjects",
				instructorDetails?.teachingSubjects?.length > 0
					? instructorDetails?.teachingSubjects
					: [{ title: "" }]
			);

			setValue(
				"languages",
				instructorDetails?.languages?.length
					? instructorDetails?.languages
					: [{ name: "", level: "Native" }]
			);
		}
	}, [instructorDetails, setValue]);

	return (
		<>
			{(updating || loadingCountries || loadingLanguages) && <Loader />}

			<div className="max-w-[640px] w-[93%] mx-auto">
				<h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
					About
				</h2>
				<p className="text-[#4B5563] sm:text-lg mb-8">
					Start creating your public tutor profile. Your progress will be
					automatically saved as you complete each section. You can return at
					any time to finish your registration.
				</p>

				<form onSubmit={handleSubmit(updateAbout)}>
					<InputField
						label="First name"
						register={register}
						fieldName="firstname"
						placeHolder="Enter your first name"
						isRequired={true}
						error={errors?.firstname?.message}
					/>

					<InputField
						label="Last name"
						register={register}
						fieldName="lastname"
						placeHolder="Enter your last name"
						isRequired={true}
						error={errors?.lastname?.message}
					/>

					<InputField
						label="Email"
						register={register}
						fieldName="email"
						fieldType="email"
						disabled
						placeHolder="Enter your email address"
						isRequired={true}
						error={errors?.email?.message}
					/>

					<CustomSelect
						label="Country of birth"
						options={countries}
						placeholder="Select country"
						className="p-5 py-[22px]"
						parentClassName="mb-7"
						name="countryOfBirth"
						control={control}
						isRequired={true}
						error={errors?.countryOfBirth?.message}
						disabled={loadingCountries}
					/>

					{teachingFields?.map((item, index) => (
						<div key={item.id} className="mb-6">
							<CustomSelect
								placeholder="Select subject"
								label="Subject"
								options={[{ value: "English", label: "English" }]}
								name={`teachingSubjects.${index}.title`}
								control={control}
								isRequired={true}
								error={errors?.teachingSubjects?.[index]?.title?.message}
								className="p-5 py-[22px]"
							/>
						</div>
					))}

					<CustomSelect
						label="Industry Specialization"
						options={industrySpecializationOptions}
						placeholder="Select industry"
						className="p-5 py-[22px]"
						parentClassName="mb-7"
						name="industrySpecialization"
						control={control}
						isRequired={true}
						error={errors?.industrySpecialization?.message}
					/>

					<div className="mb-7">
						{fields?.map((item, index) => (
							<div key={item.id} className="relative">
								<div className="flex sm:gap-5 gap-2 items-baseline mt-7">
									<CustomSelect
										placeholder="Select language"
										label="Language you speak"
										options={languages}
										control={control}
										name={`languages.${index}.name`}
										isRequired={true}
										error={errors?.languages?.[index]?.name?.message}
										className="p-5 sm:py-[24px] py-[22px]"
										disabled={loadingLanguages}
									/>

									<CustomSelect
										placeholder="Select level"
										label="Level"
										options={levelOptions}
										control={control}
										name={`languages.${index}.level`}
										isRequired={true}
										error={errors?.languages?.[index]?.level?.message}
										className="p-5 sm:py-[24px] py-[22px]"
									/>

									{index > 0 && (
										<button
											type="button"
											onClick={() => remove(index)}
											className="p-1 rounded-full h-8 w-8 mt-auto mb-[10px] flex justify-center items-center shrink-0 bg-red-100 hover:bg-red-200"
											title="Remove language"
										>
											<Trash2 size={16} className="text-red-500" />
										</button>
									)}
								</div>
							</div>
						))}
					</div>

					<p
						onClick={handleAddLanguage}
						className={`text-sm mt-[-20px] font-medium cursor-pointer text-secondary underline`}
					>
						Add another language
					</p>

					<p className="text-secondary mb-3 max-sm:text-sm mt-7">
						Phone number (Optional)
					</p>

					<PhoneInputWithCountry
						control={control}
						register={register}
						name="phoneno"
						isRequired={false}
						error={errors.phoneno?.message}
					/>

					<Button
						className="w-full h-[50px] mt-8"
						disabled={updating || loadingCountries || loadingLanguages}
						type="submit"
					>
						Save And Continue
					</Button>
				</form>
			</div>
		</>
	);
};

export default AboutTutor;
