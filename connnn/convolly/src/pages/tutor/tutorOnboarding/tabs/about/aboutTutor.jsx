import React, { useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import InputField from "@/components/inputs";
import { CustomSelect } from "@/components/select/select";
import { Trash2 } from "lucide-react";
import PhoneInputWithCountry from "@/components/inputs/phoneInputWithCountry";
import { Button } from "@/components/button/button";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { useSelector } from "react-redux";

const AboutTutor = ({ setActiveTab, instructorDetails }) => {
  const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);

  const {
    register,
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm({
    defaultValues: {
      languages: [{ name: "", level: "Native" }],
      teachingSubjects: [
        {
          title: ""
        }
      ]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "languages"
  });

  const { fields: teachingFields } = useFieldArray({
    control,
    name: "teachingSubjects"
  });

  const languageOptions = [
    { value: "English", label: "English" },
    { value: "French", label: "French" },
    { value: "Spanish", label: "Spanish" },
    { value: "German", label: "German" }
  ];

  const levelOptions = [
    { value: "basic", label: "Basic" },
    { value: "conversational", label: "Conversational" },
    { value: "fluent", label: "Fluent" },
    { value: "native", label: "native" }
  ];

  const handleAddLanguage = () => {
    append({ name: "", level: "" });
  };

  // use the generic post mutation hook to handle the login mutation
  const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
    useUpdateProfileMutation
  );

  const updateAbout = async (data) => {
    console.log(data);
    const res = await handleUpdateTutor({
      ...data,
      userId: tutorId,
      role: "tutor"
    });

    if (res) {
      setActiveTab("Photo");
    }
  };

  useEffect(() => {
    if (instructorDetails) {
      setValue("firstname", instructorDetails?.firstname);
      setValue("lastname", instructorDetails?.lastname);
      setValue("email", instructorDetails?.email);
      setValue("countryOfBirth", instructorDetails?.countryOfBirth);
      setValue("phone", instructorDetails?.phone);
      setValue("teachingSubjects", instructorDetails?.teachingSubjects);
      setValue("languages", instructorDetails?.languages);
    }
  }, [instructorDetails, setValue]);

  console.log(watch("countryOfBirth"));
  console.log(instructorDetails?.countryOfBirth);

  return (
    <div className="max-w-[640px] w-[93%] mx-auto">
      <h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
        About
      </h2>
      <p className="text-[#4B5563] sm:text-lg mb-8">
        Start creating your public tutor profile. Your progress will be
        automatically saved as you complete each section. You can return at any
        time to finish your registration.
      </p>

      <form onSubmit={handleSubmit(updateAbout)}>
        <InputField
          label="First name"
          register={register}
          fieldName="firstname"
          placeHolder="Enter your first name"
          isRequired={true}
          error={errors?.firstname?.message}
        />

        <InputField
          label="Last name"
          register={register}
          fieldName="lastname"
          placeHolder="Enter your last name"
          isRequired={true}
          error={errors?.lastname?.message}
        />

        <InputField
          label="Email"
          register={register}
          fieldName="email"
          fieldType="email"
          disabled
          placeHolder="Enter your email address"
          isRequired={true}
          error={errors?.email?.message}
        />

        <CustomSelect
          label="Country of birth"
          options={[
            { value: "Nigeria", label: "Nigeria" },
            { value: "America", label: "America" },
            { value: "Spain", label: "Spain" }
          ]}
          placeholder="Select country"
          className="p-5 py-[22px]"
          parentClassName="mb-7"
          name="countryOfBirth"
          control={control}
          isRequired={true}
          error={errors?.countryOfBirth?.message}
        />

        {teachingFields?.map((item, index) => (
          <div key={item.id} className="mb-6">
            <CustomSelect
              placeholder="Select subject"
              label="Subject"
              options={[{ value: "English", label: "English" }]}
              name={`teachingSubjects.${index}.title`}
              control={control}
              isRequired={true}
              error={errors?.teachingSubjects?.[index]?.title?.message}
              className="p-5 py-[22px]"
            />
          </div>
        ))}

        <CustomSelect
          label="Industry Specialization"
          options={[
            {
              value: "Business & Corporate English",
              label: "Business & Corporate English"
            },
            {
              value: " Medical & Healthcare English",
              label: " Medical & Healthcare English"
            },
            {
              value: " Legal & Compliance English",
              label: " Legal & Compliance English"
            },
            {
              value: "English for Tech & IT Professionals",
              label: "English for Tech & IT Professionals"
            },
            {
              value: "English for Finance, Accounting & Banking",
              label: "English for Finance, Accounting & Banking"
            },
            { value: "Aviation English", label: "Aviation English" },
            {
              value: "English for Tourism & Hospitality",
              label: "English for Tourism & Hospitality"
            },
            {
              value: "English for Academic & Research Purposes",
              label: "English for Academic & Research Purposes"
            },
            {
              value: "English for Skilled Trades",
              label: "English for Skilled Trades"
            },
            {
              value: "English for Logistics, Transport & Supply Chain",
              label: "English for Logistics, Transport & Supply Chain"
            },
            {
              value: "English for Creative Professionals",
              label: "English for Creative Professionals"
            },
            {
              value: "English for Media & Communication",
              label: "English for Media & Communication"
            },
            {
              value: "English for International Relations & NGOs",
              label: "English for International Relations & NGOs"
            },
            {
              value: "English for Government & Public Sector",
              label: "English for Government & Public Sector"
            },
            {
              value: "English for Teachers & Educators",
              label: "English for Teachers & Educators"
            },
            {
              value: "English for Performing Arts & Entertainment",
              label: "English for Performing Arts & Entertainment"
            },
            {
              value: "English for Digital & Remote Work",
              label: "English for Digital & Remote Work"
            },
            {
              value: "English for Real Estate & Property Professionals",
              label: "English for Real Estate & Property Professionals"
            },
            {
              value: "English for Culinary & Food Industry",
              label: "English for Culinary & Food Industry"
            }
          ]}
          placeholder="Select industry"
          className="p-5 py-[22px]"
          parentClassName="mb-7"
          name="industrySpecialization"
          control={control}
          isRequired={true}
          error={errors?.industrySpecialization?.message}
        />

        {/* Languages Spoken */}
        <div className="mb-7">
          {fields.map((item, index) => (
            <div key={item.id} className="relative">
              <div className="flex sm:gap-5 gap-2 items-baseline">
                <CustomSelect
                  placeholder="Select language"
                  label="Language you speak"
                  options={languageOptions}
                  control={control}
                  name={`languages.${index}.name`}
                  isRequired={true}
                  error={errors?.languages?.[index]?.name?.message}
                  className="p-5 sm:py-[24px] py-[22px]"
                />

                {index === 0 ? (
                  <InputField
                    register={register}
                    fieldName={`languages.${index}.level`}
                    placeHolder="Enter your level"
                    defaultValue="Native"
                    label="Level"
                    disabled={true}
                    isRequired={true}
                  />
                ) : (
                  <CustomSelect
                    placeholder="Select level"
                    label="Level"
                    options={levelOptions}
                    control={control}
                    name={`languages.${index}.level`}
                    isRequired={true}
                    error={errors?.level?.message}
                    className="p-5 sm:py-[24px] py-[22px]"
                  />
                )}

                {index > 0 && (
                  <button
                    type="button"
                    onClick={() => remove(index)}
                    className="p-1 rounded-full h-8 w-8 mt-auto mb-[10px] flex justify-center items-center shrink-0 bg-red-100 hover:bg-red-200"
                    title="Remove language"
                  >
                    <Trash2 size={16} className="text-red-500" />
                  </button>
                )}
              </div>
            </div>
          ))}

          <p
            onClick={handleAddLanguage}
            className="text-sm mt-[-20px] font-medium cursor-pointer text-secondary underline"
          >
            Add another language
          </p>
        </div>

        <p className="text-secondary mb-3 max-sm:text-sm">
          Phone number (Optional)
        </p>

        <PhoneInputWithCountry
          control={control}
          register={register}
          name="phone"
          isRequired={false}
          error={errors.phone?.message}
        />

        <Button
          className="w-full h-[50px] mt-8"
          disabled={updating}
          type="submit"
        >
          Save And Continue
        </Button>
      </form>
    </div>
  );
};

export default AboutTutor;
