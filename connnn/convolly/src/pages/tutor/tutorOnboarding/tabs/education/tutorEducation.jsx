import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/button/button";
import { CustomSelect } from "@/components/select/select";
import InputField from "@/components/inputs";
import { useForm, useFieldArray } from "react-hook-form";
import uploadIcon from "@/assets/svgs/uploadIcon.svg";
import CheckboxInput from "@/components/inputs/checkboxInput";
import { Trash2 } from "lucide-react";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import { convertToBase64 } from "@/utils/utils";
import { useSelector } from "react-redux";

const TutorEducation = ({ setActiveTab, instructorDetails }) => {
  const tutorId = useSelector((state) => state?.app?.userInfo?.user?.id);

  const {
    register,
    control,
    handleSubmit,
    setValue,
    formState: { errors }
  } = useForm();

  const [uploadedFiles, setUploadedFiles] = useState({});
  console.log(uploadedFiles);

  const { fields, append, remove } = useFieldArray({
    control,
    name: "academics"
  });

  const { handlePost: handleUpdateTutor, isLoading: updating } = usePost(
    useUpdateProfileMutation
  );

  const handleAddCertificate = () => {
    append({
      university: "",
      startDate: "",
      degree: "",
      degreeType: "",
      endDate: "",
      fileUpload: ""
    });
  };

  const handleFileChange = async (e, index) => {
    const file = e.target.files[0];
    if (file) {
      const base64 = await convertToBase64(file);

      // Save file name and base64 in state (optional for UI)
      setUploadedFiles((prev) => ({
        ...prev,
        [index]: { name: file.name, base64 }
      }));

      // Update react-hook-form state
      setValue(`academics[${index}].fileUpload`, {
        name: file.name,
        base64
      });
    }
  };

  const updateEducation = async (data) => {
    const formattedEducation = data.academics.map((edu) => ({
      ...edu,
      // fileUpload: edu.fileUpload?.base64 || ""
      fileUpload: ""
    }));

    const res = await handleUpdateTutor({
      academics: formattedEducation,
      userId: tutorId,
      role: "tutor"
    });

    if (res) {
      setActiveTab("Description");
    }
  };

  useEffect(() => {
    if (instructorDetails?.academics?.length) {
      // try to process the data the data to be in the shape of how it was sent initially
      const formattedAcademics = instructorDetails.academics.map((edu) => ({
        ...edu,
        // return only year
        startDate: edu.startDate.slice(0, 4),
        endDate: edu.endDate.slice(0, 4),
        fileUpload: edu.fileUpload
          ? {
              name: edu.fileUpload.name || "",
              base64: edu.fileUpload.base64 || ""
            }
          : ""
      }));

      // Set default values for academics
      setValue("academics", formattedAcademics);
    }
  }, [instructorDetails, setValue]);

  return (
    <form
      className="max-w-[528px] w-[93%] mx-auto"
      onSubmit={handleSubmit(updateEducation)}
    >
      <h2 className="sm:text-4xl text-3xl font-bold mb-5 sm:mt-8 lg:mt-0">
        Education (Optional)
      </h2>
      <p className="text-[#4B5563] sm:text-lg mb-8">
        Tell students more about the higher education that you've completed or
        are working on
      </p>

      {fields?.map((cert, index) => (
        <div key={cert.id}>
          {index > 0 && (
            <button
              type="button"
              onClick={() => remove(index)}
              className="p-1 rounded-full h-8 w-8 mt-auto mb-[10px] ml-auto flex justify-center items-center shrink-0 bg-red-100 hover:bg-red-200"
              title="Remove language"
            >
              <Trash2 size={16} className="text-red-500" />
            </button>
          )}

          <InputField
            register={register}
            fieldName={`academics.${index}.university`}
            placeHolder="Eg Bristol University"
            label="University"
            isRequired={true}
          />

          <InputField
            register={register}
            fieldName={`academics.${index}.degree`}
            placeHolder="Eg Bachelor's degree in English Language"
            label="Degree"
            isRequired={true}
          />

          <CustomSelect
            placeholder="Choose a degree type"
            label="Degree type"
            options={[
              { value: "Bachelor's Degrees", label: "Bachelor's Degrees" },
              { value: "Master's Degrees", label: "Master's Degrees" }
            ]}
            name={`academics.${index}.degreeType`}
            control={control}
            isRequired={true}
            error={errors?.degreeType?.message}
            className="p-5 py-[22px]"
            parentClassName="mb-7"
          />

          <div className="flex sm:gap-5 gap-2">
            <CustomSelect
              placeholder="Select year"
              label="Start year"
              options={[
                { value: "2000", label: "2000" },
                { value: "2001", label: "2001" },
                { value: "2002", label: "2002" },
                { value: "2003", label: "2003" },
                { value: "2004", label: "2004" }
              ]}
              name={`academics.${index}.startDate`}
              control={control}
              isRequired={true}
              error={errors?.startDate?.message}
              className="p-5 py-[22px]"
              parentClassName="mb-7"
            />

            <CustomSelect
              placeholder="Select year"
              label="End year"
              options={[
                { value: "2000", label: "2000" },
                { value: "2001", label: "2001" },
                { value: "2002", label: "2002" },
                { value: "2003", label: "2003" },
                { value: "2004", label: "2004" }
              ]}
              name={`academics.${index}.endDate`}
              control={control}
              isRequired={true}
              error={errors?.endDate?.message}
              className="p-5 py-[22px]"
              parentClassName="mb-7"
            />
          </div>

          <p className="text-secondary mb-3 max-sm:text-sm">
            Upload your certificate here
          </p>

          <label
            className="w-full border border-dashed border-[#D2D2D2] sm:p-5 p-3 flex flex-col items-center rounded-lg cursor-pointer mb-7"
            htmlFor={`upload-cert-${index}`}
          >
            <img src={uploadIcon} alt="upload icon" />
            <p className="text-[#4B5563] sm:text-lg mb-2">
              Choose a file or drag and drop it here
            </p>
            <p className="text-[#4B5563] sm:text-lg mb-5">
              Jpeg, Png, Pdf Max 5mb
            </p>

            <span className="text-primary font-bold sm:text-xl text-lg">
              Browse
            </span>

            <input
              type="file"
              id={`upload-cert-${index}`}
              className="hidden"
              aria-label="upload a certificate"
              onChange={(e) => handleFileChange(e, index)}
            />
          </label>

          {uploadedFiles[index] && (
            <div className="flex items-center gap-2 mb-6 text-[#4B5563] text-sm mt-[-14px]">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
                className="w-5 h-5 text-primary"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M19.5 14.25v3.375A2.625 2.625 0 0116.875 20.25H7.125A2.625 2.625 0 014.5 17.625V6.375A2.625 2.625 0 017.125 3.75h5.25L19.5 9v2.25"
                />
              </svg>
              <span>{uploadedFiles[index].name}</span>
            </div>
          )}

          {index == fields?.length - 1 && (
            <p
              onClick={handleAddCertificate}
              className="text-sm mt-[-20px] font-medium text-secondary underline mb-10 cursor-pointer"
            >
              Add another certificate
            </p>
          )}
        </div>
      ))}

      <div className="sm:flex gap-5">
        <Button
          className="w-full h-[50px] bg-white border-[#D2D2D2] text-secondary border hover:bg-white"
          onClick={() => setActiveTab("Certification")}
        >
          Back
        </Button>

        <Button className="w-full h-[50px]" disabled={updating} type="submit">
          Save And Continue
        </Button>
      </div>
    </form>
  );
};

export default TutorEducation;
