import React, { useEffect, useRef } from "react";
import { useForm } from "react-hook-form";
import { useDispatch, useSelector } from "react-redux";
import userImage from "../../../../../assets/images/tutor2.png";
import { But<PERSON> } from "@/components/button/button";
import Loader from "@/components/loader/loader";
import { useGetUpdateStudentProfileMutation } from "@/redux/slices/student/studentSettingsApiSlice";
import { useUploadProfileImageMutation, useGetProfileImageQuery, useDeleteProfileImageMutation } from "@/redux/slices/student/profileImageApiSlice";
import { toast } from "react-toastify";
import PhoneInputWithCountry from "@/components/inputs/phoneInputWithCountry";
import { CustomSelect } from "@/components/select/select";
import { Trash2 } from "lucide-react";
import { convertToBase64 } from "@/utils/utils";
import { setUserInfo } from "@/redux/appSlice";

const SettingsProfile = () => {
	const dispatch = useDispatch();
	const user = useSelector((state) => state?.app?.userInfo?.user);
	const userInfo = useSelector((state) => state?.app?.userInfo);

	// Profile update hook
	const [updateStudentProfile, { isLoading: isUpdating }] = useGetUpdateStudentProfileMutation();

	// Profile image hooks
	const fileInputRef = useRef(null);
	const [uploadProfileImage, { isLoading: isUploading }] = useUploadProfileImageMutation();
	const { data: profileImageData, refetch: refetchProfileImage } = useGetProfileImageQuery();
	const [deleteProfileImage, { isLoading: isDeleting }] = useDeleteProfileImageMutation();

	const {
		register,
		handleSubmit,
		reset,
		control,
		formState: { errors },
	} = useForm();

	const timeZones = [
		"Pacific/Midway",
		"America/Adak",
		"America/Anchorage",
		"America/Los_Angeles",
		"America/Denver",
		"America/Chicago",
		"America/New_York",
		"America/Sao_Paulo",
		"Atlantic/Reykjavik",
		"Europe/London",
		"Europe/Berlin",
		"Europe/Moscow",
		"Africa/Lagos",
		"Africa/Cairo",
		"Asia/Dubai",
		"Asia/Kolkata",
		"Asia/Jakarta",
		"Asia/Shanghai",
		"Asia/Tokyo",
		"Asia/Seoul",
		"Australia/Sydney",
		"Pacific/Auckland",
	];

	// Initialize form with user data from Redux store
	useEffect(() => {
		if (user) {
			reset({
				firstname: user?.firstname || "",
				lastname: user?.lastname || "",
				email: user?.email || "",
				phone: user?.phone || "",
				timeZone: user?.timeZone || "",
			});
		}
	}, [user, reset]);

	const onSubmit = async (formData) => {
		try {
			const response = await updateStudentProfile({
				userId: user?.id,
				...formData,
			}).unwrap();

			if (response?.success) {
				// Update Redux store with new user data
				dispatch(setUserInfo({
					...userInfo,
					user: {
						...user,
						...response.data,
					}
				}));

				toast.success("Profile updated successfully!");

				// Refetch profile image to ensure it's up to date
				refetchProfileImage();
			}
		} catch (error) {
			console.error("Profile update error:", error);
			toast.error(error?.data?.message || "Failed to update profile");
		}
	};

	// Profile image handlers
	const handleImageUpload = async (event) => {
		const file = event.target.files[0];
		if (!file) return;

		// Validate file type
		const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
		if (!allowedTypes.includes(file.type)) {
			toast.error('Invalid file type. Only JPEG, PNG, and WebP images are allowed.');
			return;
		}

		// Validate file size (2MB)
		if (file.size > 2 * 1024 * 1024) {
			toast.error('File size too large. Maximum size is 2MB.');
			return;
		}

		try {
			// Convert to base64
			const base64Image = await convertToBase64(file);

			const response = await uploadProfileImage({
				imageBase64: base64Image
			}).unwrap();

			if (response.success) {
				// Update Redux store with new image URL
				dispatch(setUserInfo({
					...userInfo,
					user: {
						...user,
						image: response.data.imageUrl,
					}
				}));

				toast.success('Profile image uploaded successfully!');
				refetchProfileImage();
			}
		} catch (error) {
			console.error('Error uploading image:', error);
			toast.error(error?.data?.message || 'Failed to upload image');
		}
	};

	const handleDeleteImage = async () => {
		if (window.confirm('Are you sure you want to delete your profile image?')) {
			try {
				const response = await deleteProfileImage().unwrap();
				if (response.success) {
					// Update Redux store to remove image
					dispatch(setUserInfo({
						...userInfo,
						user: {
							...user,
							image: null,
						}
					}));

					toast.success('Profile image deleted successfully!');
					refetchProfileImage();
				}
			} catch (error) {
				console.error('Error deleting image:', error);
				toast.error('Failed to delete image');
			}
		}
	};

	const handleUploadClick = () => {
		fileInputRef.current?.click();
	};

	const timeZoneOptions = timeZones.map((tz) => ({
		label: tz,
		value: tz,
	}));

	const currentTimezone = Array.isArray(user?.location?.address)
		? [{ label: user.location.address[0], value: user.location.address[0] }]
		: [];

	console.log("currentTimezone", currentTimezone);
	console.log("timeZoneOptions");

	return (
		<div className="md:max-w-[528px] w-auto">
			<form
				onSubmit={handleSubmit(onSubmit)}
				className="flex text-[18px] flex-col w-auto"
			>
				{/* Profile Image Upload Section */}
				<div className="flex gap-4">
					<div className="border rounded-md w-[145px] h-[145px] overflow-hidden relative">
						<img
							src={profileImageData?.data?.imageUrl || userImage}
							alt="User"
							className="object-cover w-full h-full"
						/>
						{profileImageData?.data?.imageUrl && (
							<button
								onClick={handleDeleteImage}
								disabled={isDeleting}
								className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
							>
								<Trash2 className="w-3 h-3" />
							</button>
						)}
					</div>
					<div className="flex flex-col items-center">
						<div className="flex flex-col gap-2">
							<button
								type="button"
								onClick={handleUploadClick}
								disabled={isUploading}
								className="px-6 py-2 border rounded-md cursor-pointer hover:bg-gray-50 disabled:opacity-50"
							>
								{isUploading ? <Loader size={16} /> : "Upload Photo"}
							</button>
							<input
								ref={fileInputRef}
								type="file"
								hidden
								accept="image/jpeg,image/jpg,image/png,image/webp"
								onChange={handleImageUpload}
								aria-label="upload-photo"
							/>
						</div>
						<p className="text-[#4B5563] text-sm text-center">
							Maximum size – 2MB <br />
							JPG, PNG, or WebP format
						</p>
					</div>
				</div>

				<div className="text-[#1A1A40] mt-6 space-y-4">
					{/* First Name */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							First Name
						</label>
						<input
							type="text"
							{...register("firstname", { required: "First name is required" })}
							className="border rounded-md w-full px-4 py-2"
						/>
						{errors.firstname && (
							<p className="text-red-500 text-sm mt-1">
								{errors.firstname.message}
							</p>
						)}
					</div>

					{/* Last Name */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							Last Name
						</label>
						<input
							type="text"
							{...register("lastname", { required: "Last name is required" })}
							className="border rounded-md w-full px-4 py-2"
						/>
						{errors.lastname && (
							<p className="text-red-500 text-sm mt-1">
								{errors.lastname.message}
							</p>
						)}
					</div>

					{/* Email */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							Email
						</label>
						<input
							type="email"
							{...register("email", {
								required: "Email is required",
								pattern: {
									value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
									message: "Invalid email address",
								},
							})}
							className="border rounded-md w-full px-4 py-2"
						/>
						{errors.email && (
							<p className="text-red-500 text-sm mt-1">
								{errors.email.message}
							</p>
						)}
					</div>

					{/* Phone Number - Fixed */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							Phone Number
						</label>
						<PhoneInputWithCountry
							control={control}
							name="phone"
							defaultValue={user?.phone || ""}
							rules={{
								required: "Phone number is required",
								validate: (value) => {
									const phoneRegex = /^\+?[1-9]\d{1,14}$/; // E.164 format
									return phoneRegex.test(value) || "Invalid phone number";
								},
							}}
							className="border rounded-md w-full px-4 py-2"
						/>
						{errors.phone && (
							<p className="text-red-500 text-sm mt-1">
								{errors.phone.message}
							</p>
						)}
					</div>

					{/* Time Zone - Fixed */}
					<div>
						<label className="block mb-1 text-[#1A1A40] text-[18px] font-medium">
							Time Zone
						</label>
						<CustomSelect
							name="timeZone"
							control={control}
							options={timeZoneOptions}
							defaultValue={
								user?.timeZone
									? { label: user.timeZone, value: user.timeZone }
									: null
							}
							rules={{ required: "Time zone is required" }}
							placeholder={currentTimezone[0]?.label || "Select Time Zone"}
							className="w-full"
						/>

						{errors.timeZone && (
							<p className="text-red-500 text-sm mt-1">
								{errors.timeZone.message}
							</p>
						)}
					</div>
				</div>

				<div className="mt-6">
					<Button
						className="w-full h-[50px] mb-3"
						type="submit"
						disabled={isUpdating}
					>
						{isUpdating ? <Loader size={24} /> : "Save Changes"}
					</Button>
				</div>
			</form>
		</div>
	);
};

export default SettingsProfile;
