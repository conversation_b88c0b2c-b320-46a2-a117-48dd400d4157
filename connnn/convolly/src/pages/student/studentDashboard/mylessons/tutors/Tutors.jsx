import React, { useMemo } from "react";
import useGet from "@/hooks/useGet";
import { useGetTutorsQuery } from "@/redux/slices/student/findTutorApiSlice";
import { useGetStudentSubscriptionsQuery } from "@/redux/slices/student/subscriptionApiSlice";
import { useGetClassesQuery } from "@/redux/slices/student/classesApiSlice";
import { useNavigate } from "react-router-dom";
import Loader from "@/components/loader/loader";
import { capitalizeWords, formatTutorName } from "@/utils/utils";
import userVector from "@/assets/svgs/userVector.svg";
import { useSelector } from "react-redux";

const Tutors = () => {
	const navigate = useNavigate();

	// Get current user info
	const user = useSelector((state) => state?.app?.userInfo?.user);
	const userId = user?.id;

	const hasUsedFreeTrial = useSelector(
		(state) => state?.app?.userInfo?.user?.hasUsedFreeTrial
	);

	// Only make API calls if user is a student
	const isStudent = user?.role === "student";

	// Fetch all tutors
	const { data: tutors, isLoading: tutorsLoading } = useGet(
		useGetTutorsQuery,
		"",
		isStudent
	);

	// Fetch student's subscriptions
	const { data: subscriptions, isLoading: subscriptionsLoading } =
		useGetStudentSubscriptionsQuery(userId, {
			skip: !userId || !isStudent,
		});

	// Fetch student's classes/lessons
	const { data: classes, isLoading: classesLoading } = useGet(
		useGetClassesQuery,
		"",
		isStudent
	);

	// Get tutor IDs from subscriptions and classes
	const interactedTutorIds = useMemo(() => {
		const tutorIds = new Set();

		// Add tutors from subscriptions
		if (subscriptions?.data) {
			subscriptions.data.forEach((subscription) => {
				if (subscription.tutorId?.id) {
					tutorIds.add(subscription.tutorId.id);
				} else if (subscription.tutorId) {
					// Handle case where tutorId is just the ID string
					tutorIds.add(subscription.tutorId);
				}
			});
		}

		// Add tutors from classes/lessons (including free trials)
		if (classes?.bookings) {
			classes.bookings.forEach((booking) => {
				if (booking.tutorId?.id) {
					tutorIds.add(booking.tutorId.id);
				} else if (booking.tutorId) {
					// Handle case where tutorId is just the ID string
					tutorIds.add(booking.tutorId);
				}
			});
		}

		return Array.from(tutorIds);
	}, [subscriptions, classes]);

	// Filter tutors to only show those the student has interacted with
	const filteredTutors = useMemo(() => {
		if (!tutors || interactedTutorIds.length === 0) {
			return [];
		}

		return tutors.filter(
			(tutor) =>
				tutor.approvalStatus === "approved" &&
				interactedTutorIds.includes(tutor.id)
		);
	}, [tutors, interactedTutorIds]);

	const isLoading = tutorsLoading || subscriptionsLoading || classesLoading;

	console.log(hasUsedFreeTrial);

	return (
		<div className="">
			{isLoading && <Loader />}

			{!isLoading && filteredTutors.length === 0 && (
				<div className="text-center py-8">
					<p className="text-gray-500 text-lg mb-2">No tutors found</p>
					<p className="text-gray-400 text-sm">
						You haven't subscribed to any tutors or taken any lessons yet.
					</p>
				</div>
			)}

			{filteredTutors?.map((tutor, index) => (
				<div className="border rounded-md p-4 mb-4" key={index}>
					{/* Top row for tutor info and price (always visible) */}
					<div className="flex justify-between items-center mb-3 sm:mb-0">
						<div className="flex items-center">
							<img
								src={tutor.image || userVector}
								alt=""
								className="object-cover w-12 h-12 sm:w-14 sm:h-14 mr-4 sm:mr-6 rounded-md"
							/>
							<div>
								<p className="text-[#1A1A40] text-sm sm:text-md font-semibold">
									{formatTutorName(tutor.fullname)}
								</p>
								<p className="text-xs sm:text-sm text-[#4B5563]">
									Teaches {tutor.languages.map((lang) => lang.name).join(", ")}
								</p>
							</div>
						</div>

						<div className="text-center">
							<p className="text-[#1A1A40] text-sm sm:text-md font-semibold">
								$ {tutor.basePrice}
							</p>
							<p className="text-[#4B5563] text-xs sm:text-sm">per lesson</p>
						</div>
						<div className="hidden sm:block">
							<button
								className={`p-2 px-4 rounded-md min-w-[130px] text-sm ${
									!hasUsedFreeTrial
										? "bg-white text-primary border border-primary"
										: "bg-primary text-white"
								}`}
								onClick={
									!hasUsedFreeTrial
										? () => navigate(`/student/my-lessons/tutors/${tutor.id}`)
										: () =>
												navigate(
													`/student/my-lessons/tutors/${tutor.id}/subscribe`
												)
								}
							>
								{!hasUsedFreeTrial ? "Schedule free trial" : "Subscribe"}
							</button>
						</div>
					</div>

					{/* Button row (full width on mobile, auto width on desktop) */}
					<div className="sm:hidden mt-3">
						<button
							className={`w-full py-2 rounded-md text-sm ${
								!hasUsedFreeTrial
									? "bg-white text-primary border border-primary"
									: "bg-primary text-white"
							}`}
							onClick={
								!hasUsedFreeTrial
									? () => navigate(`/student/my-lessons/tutors/${tutor.id}`)
									: () =>
											navigate(
												`/student/my-lessons/tutors/${tutor.id}/subscribe`
											)
							}
						>
							{!hasUsedFreeTrial ? "Schedule free trial" : "Subscribe"}
						</button>
					</div>
				</div>
			))}
		</div>
	);
};

export default Tutors;
