import React, { useState, useEffect } from "react";
import { CustomSelect } from "@/components/select/select";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import MultiSelect from "@/components/select/multiSelect";
import Morning from "@/assets/svgs/morning";
import Afternoon from "@/assets/svgs/afternoon";
import Evening from "@/assets/svgs/evening";
import { Button } from "@/components/button/button";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import SuccessModal from "@/components/modal/successModal";
import { useNavigate } from "react-router-dom";
import Loader from "@/components/loader/loader";

const PageTwo = ({ setActiveTab, studentDetails, refetchStudentDetails }) => {
  const studentId = useSelector((state) => state?.app?.userInfo?.user?.id);
  const navigate = useNavigate();

  const [selectedDurations, setSelectedDurations] = useState([]);
  const [showSuccess, setShowSuccess] = useState(false);

  const {
    register,
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm();

  const levelOptions = [
    { value: "basic", label: "Basic" },
    { value: "conversational", label: "Conversational" },
    { value: "fluent", label: "Fluent" },
    { value: "native", label: "native" }
  ];

  const timezones = [
    {
      label: "18:03 (GMT+0) - Africa/Lagos",
      value: "18:03 (GMT+0) - Africa/Lagos"
    },
    {
      label: "13:03 (GMT+0) - America/New York",
      value: "13:03 (GMT+0) - America/New York"
    },
    {
      label: "18:03 (GMT+0) - Europe/London",
      value: "18:03 (GMT+0) - Europe/London"
    },
    {
      label: "22:33 (GMT+0) - Asia/Kolkata",
      value: "22:33 (GMT+0) - Asia/Kolkata"
    },
    {
      label: "02:03 (GMT+0) - Asia/Tokyo",
      value: "02:03 (GMT+0) - Asia/Tokyo"
    },
    {
      label: "03:03 (GMT+0) - Australia/Sydney",
      value: "03:03 (GMT+0) - Australia/Sydney"
    }
  ];

  const daysOfWeek = [
    { label: "Monday", value: "Monday" },
    { label: "Tuesday", value: "Tuesday" },
    { label: "Wednesday", value: "Wednesday" },
    { label: "Thursday", value: "Thursday" },
    { label: "Friday", value: "Friday" },
    { label: "Saturday", value: "Saturday" },
    { label: "Sunday", value: "Sunday" }
  ];

  const timeOfDay = [
    { name: "Morning", time: "9-11", Icon: Morning },
    { name: "Afternoon", time: "12-18", Icon: Afternoon },
    { name: "Evening", time: "19-22", Icon: Evening }
  ];

  const { handlePost: handleUpdateStudent, isLoading: updating } = usePost(
    useUpdateProfileMutation
  );

  const onboardingComplete = () => {
    setShowSuccess(false);
    navigate("/student-dashboard");
  };

  const updatePageTwo = async (data) => {
    console.log(data);

    // format the availablity into how the backend expects it
    const timeAvailable = selectedDurations.map((label) => {
      const timeSlot = timeOfDay.find(
        (t) => t.name.toLowerCase() === label.toLowerCase()
      );
      const [from, to] = timeSlot.time.split("-");

      return {
        label,
        from,
        to
      };
    });

    // remove the level and put it in the languages array like the backend needs
    const { level, ...rest } = data;

    const res = await handleUpdateStudent({
      ...rest,
      timeAvailable,
      languages: [{ name: "English", level }],
      location: { state: data?.location },
      userId: studentId,
      role: "student"
    });

    if (res) {
      setShowSuccess(true);
      refetchStudentDetails();
    }
  };

  return (
    <>
      {updating && <Loader />}

      <SuccessModal
        isOpen={showSuccess}
        onClose={onboardingComplete}
        title="Congratulations"
        message="You have onboarded successfully"
        onButtonClick={onboardingComplete}
        buttonText="Continue"
      />

      <form onSubmit={handleSubmit(updatePageTwo)}>
        <CustomSelect
          placeholder="Select your English level"
          label="What is your current English level?"
          options={levelOptions}
          control={control}
          name="level"
          isRequired={true}
          error={errors?.level?.message}
          className="p-5 py-[22px]"
          parentClassName="mb-7"
        />

        <MultiSelect
          options={daysOfWeek}
          placeholder="Select days of the week you will take lessons"
          label="When can you take lessons?"
          control={control}
          name="daysAvailable"
          parentClassName="mb-7"
        />

        <p className="max-sm:text-sm text-secondary mb-2">
          What is your preferred lesson duration?
        </p>

        {timeOfDay.map(({ name, time, Icon }, index) => (
          <label
            className={`flex items-center gap-2 w-fit ${
              index < timeOfDay.length - 1 ? "mb-2" : "mb-7"
            }`}
            key={name}
          >
            <input
              type="checkbox"
              value={name.toLowerCase()}
              onChange={(e) => {
                const { checked, value } = e.target;
                setSelectedDurations((prev) =>
                  checked
                    ? [...prev, value]
                    : prev.filter((val) => val !== value)
                );
              }}
              className="custom-checkbox text-sm border border-[#E8E8E8] bg-white rounded-xl focus:outline-none"
            />
            <div className="sm:text-base text-sm flex gap-2">
              {<Icon />}
              {name + " (" + time + " )"}
            </div>
          </label>
        ))}

        <CustomSelect
          placeholder="13:03 (GMT+0) - America/New York"
          label="Choose your time zone"
          options={timezones}
          control={control}
          name="location"
          isRequired={true}
          error={errors?.location?.message}
          className="p-5 py-[22px]"
          parentClassName="mb-7"
        />

        <div className="sm:flex gap-5 mt-10">
          <Button
            className="w-full h-[50px] bg-white border-[#D2D2D2] text-secondary border hover:bg-white"
            onClick={() => setActiveTab("pageOne")}
          >
            Back
          </Button>

          <Button className="w-full h-[50px]" disabled={updating} type="submit">
            Save And Continue
          </Button>
        </div>
      </form>
    </>
  );
};

export default PageTwo;
