import React, { useEffect } from "react";
import { CustomSelect } from "@/components/select/select";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import MultiSelect from "@/components/select/multiSelect";
import { Button } from "@/components/button/button";
import usePost from "@/hooks/usePost";
import { useUpdateProfileMutation } from "@/redux/slices/onboardingApiSlice";
import Loader from "@/components/loader/loader";

const PageOne = ({ setActiveTab, studentDetails, refetchStudentDetails }) => {
  const studentId = useSelector((state) => state?.app?.userInfo?.user?.id);

  const {
    register,
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm();

  console.log(errors);

  const industryOptions = [
    { value: true, label: "Yes, I do" },
    { value: false, label: "No" }
  ];

  const lessonDurationOptions = [
    { value: "25mins", label: "25mins" },
    { value: "50mins", label: "50mins" },
    { value: "both", label: "Both" }
  ];

  const languageOptions = [
    { value: "English", label: "English" },
    { value: "French", label: "French" },
    { value: "Spanish", label: "Spanish" },
    { value: "German", label: "German" }
  ];

  const { handlePost: handleUpdateStudent, isLoading: updating } = usePost(
    useUpdateProfileMutation
  );

  const updatePageOne = async (data) => {
    console.log(data);
    const res = await handleUpdateStudent({
      ...data,
      nativeLanguage: { name: data?.nativeLanguage },
      userId: studentId,
      role: "student"
    });

    if (res) {
      setActiveTab("pageTwo");
      refetchStudentDetails();
    }
  };

  useEffect(() => {
    if (studentDetails) {
      setValue("learningReasons", studentDetails?.learningReasons);
      setValue(
        "needIndustryKnowledge",
        String(studentDetails?.needIndustryKnowledge)
      );
      setValue(
        "preferredLessonDuration",
        studentDetails?.preferredLessonDuration
      );
      setValue("nativeLanguage", studentDetails?.nativeLanguage);
      setValue("skillsToImprove", studentDetails?.skillsToImprove);
    }
  }, [studentDetails, setValue]);

  return (
    <>
    {updating && <Loader />}

    <form onSubmit={handleSubmit(updatePageOne)}>
      <MultiSelect
        options={[
          {
            label: "Job promotion or career growth",
            value: "Job promotion or career growth"
          },
          {
            label: "Communication with international clients",
            value: "Communication with international clients"
          },
          {
            label: "Relocating or studying abroad",
            value: "Relocating or studying abroad"
          },
          {
            label: "Preparing for job interviews",
            value: "Preparing for job interviews"
          },
          {
            label: "Collaboration with global teams",
            value: "Collaboration with global teams"
          },
          {
            label: "Presentations and meetings",
            value: "Presentations and meetings"
          },
          {
            label: "Enhancing technical ideas or products",
            value: "Enhancing technical ideas or products"
          }
        ]}
        placeholder="Select area of focus"
        label="What is your main reason for improving your English?"
        control={control}
        name="learningReasons"
        parentClassName="mb-7"
      />

      <MultiSelect
        options={[
          { label: "Accent reduction", value: "Accent reduction" },
          { label: "Phonetics", value: "Phonetics" },
          {
            label: "Listening and Reading comprehension",
            value: "Listening and Reading comprehension"
          },
          { label: "Conversational fluency", value: "Conversational fluency" },
          {
            label: "Grammar and Vocabulary focus",
            value: "Grammar and Vocabulary focus"
          },
          { label: "Academic writing", value: "Academic writing" }
        ]}
        placeholder="Select your English goal"
        label="What English skills do you want to improve?"
        control={control}
        name="skillsToImprove"
        parentClassName="mb-7"
      />

      <p className="max-sm:text-sm text-secondary mb-2">
        Do you need industry-specific knowledge?
      </p>

      {industryOptions.map(({ label, value }, index) => (
        <label
          className={`flex items-center gap-2 w-fit ${
            index < industryOptions.length - 1 ? "mb-2" : "mb-7"
          }`}
          key={value}
        >
          <input
            type="radio"
            value={value}
            {...register("needIndustryKnowledge")}
            className="custom-checkbox text-sm border border-[#E8E8E8] bg-white rounded-xl focus:outline-none"
          />
          <span className="sm:text-base text-sm">{label}</span>
        </label>
      ))}

      <p className="max-sm:text-sm text-secondary mb-2">
        What is your preferred lesson duration?
      </p>

      {lessonDurationOptions.map(({ label, value }, index) => (
        <label
          className={`flex items-center gap-2 w-fit ${
            index < lessonDurationOptions.length - 1 ? "mb-2" : "mb-7"
          }`}
          key={value}
        >
          <input
            type="radio"
            value={value}
            {...register("preferredLessonDuration")}
            className="custom-checkbox text-sm border border-[#E8E8E8] bg-white rounded-xl focus:outline-none"
          />
          <span className="sm:text-base text-sm">{label}</span>
        </label>
      ))}

      <CustomSelect
        placeholder="Select your language"
        label="What is your native language?"
        options={languageOptions}
        control={control}
        name="nativeLanguage"
        isRequired={true}
        error={errors?.language?.message}
        className="p-5 py-[22px]"
        parentClassName="mb-10"
      />

      <Button
        className="w-full h-[50px] mt-5"
        disabled={updating}
        type="submit"
      >
        Save And Continue
      </Button>
    </form>
    </>
  );
};

export default PageOne;
