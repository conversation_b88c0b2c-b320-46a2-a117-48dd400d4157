import React, { useEffect } from "react";
import logo from "../../assets/svgs/logo.svg";
import signWithGoogle from "../../assets/svgs/signWithGoogle.svg";
import signWithApple from "../../assets/svgs/signWithApple.svg";
import InputField from "../../components/inputs";
import { useForm } from "react-hook-form";
import loginBackground from "../../assets/images/loginBackground.png";
import { Button } from "../../components/button/button";
import { useNavigate } from "react-router-dom";
import usePost from "../../hooks/usePost";
import { useLoginMutation } from "../../redux/slices/authApiSlice";
import { jwtDecode } from "jwt-decode";
import { useDispatch } from "react-redux";
import { setUserInfo } from "@/redux/appSlice";
import Loader from "@/components/loader/loader";

const LoginPage = () => {
  const {
    register,
    handleSubmit,
    control,
    reset,
    formState: { errors }
  } = useForm();

  const navigate = useNavigate();
  const dispatch = useDispatch();
  const GOOGLE_CLIENT_ID = import.meta.env.VITE_GOOGLE_CLIENT_ID;

  // use the generic post mutation hook to handle the login mutation
  const { handlePost: handleLogin, isLoading: loggingIn } =
    usePost(useLoginMutation);

  const loginUser = async (data) => {
    const res = await handleLogin(data);

    if (res) {
      reset();
      dispatch(setUserInfo(res?.data));
      if (res?.data?.user?.role == "student") {
        navigate("/student-onboarding");
      } else {
        navigate("/tutor-onboarding");
      }
    }
  };

  const handleGoogleCallback = async (response) => {
    // Decode the ID token to get user info
    const decodedToken = jwtDecode(response?.credential);

    // Extract user info from the decoded token
    const { given_name, family_name, email } = decodedToken;

    const userData = {
      firstname: given_name,
      lastname: family_name,
      email: email,
      provider: "google"
    };

    try {
      const data = await loginUser(userData);
    } catch (err) {
      console.error("Error during Google signin:", err);
    }
  };

  useEffect(() => {
    if (window.google && GOOGLE_CLIENT_ID) {
      window.google.accounts.id.initialize({
        client_id: GOOGLE_CLIENT_ID,
        callback: handleGoogleCallback
      });

      window.google.accounts.id.renderButton(
        document.getElementById("googleSignInBtn"),
        {
          theme: "outline",
          size: "large",
          shape: "pill",
          width: "100%"
        }
      );
    }
  }, []);

  return (
    <div className="flex min-h-screen overflow-hidden relative">
      {loggingIn && <Loader />}

      <div
        className="hidden lg:flex fixed left-0 top-0 w-1/2 h-screen flex-col z-10"
        style={{
          backgroundImage: `url(${loginBackground})`,
          backgroundSize: "cover",
          backgroundPosition: "center"
        }}
      >
        <div className="absolute inset-0 bg-black/30 z-0" />

        <div className="relative z-10 w-full h-full p-12 flex flex-col justify-end">
          <h2 className="text-2xl text-white font-bold mb-2 leading-[50px]">
            Share Your Knowledge. Inspire Learners Worldwide.
          </h2>
          <p className="text-white max-w-[600px]">
            Join our global network of English tutors, connect with eager
            students, and earn by doing what you love—teaching.
          </p>
        </div>
      </div>

      <form
        className="ml-auto w-full lg:w-1/2 overflow-y-auto flex flex-col justify-center lg:p-12 md:p-8 p-5 min-h-screen"
        onSubmit={handleSubmit(loginUser)}
      >
        <img
          src={logo}
          alt="convolly logo"
          className="sm:w-[190px] w-[120px] mb-10"
        />

        <h2 className="md:text-4xl text-2xl font-bold mb-2 sm:leading-[50px] leading-[30px]">
          Login to your account
        </h2>
        <p className="text-[#4B5563] sm:text-lg mb-8">
          Login to continue your journey
        </p>

        <div className="sm:flex items-center gap-5 mb-5">
          <div
            className="flex border-[1px] border-[#E8E8E8] rounded-[30px] gap-2 cursor-pointer grow p-1 px-3 justify-center max-sm:mb-5"
            id="googleSignInBtn"
          >
            <img src={signWithGoogle} alt="sign with google" />
            <span className="sm:text-lg text-[#121212]">
              Sign in with Google
            </span>
          </div>

          <div className="flex border-[1px] border-[#E8E8E8] rounded-[30px] gap-2 cursor-pointer grow p-2 px-4 justify-center">
            <img src={signWithApple} alt="sign with apple" />
            <span className="sm:text-lg text-[#121212]">
              Sign in with Apple
            </span>
          </div>
        </div>

        <div className="flex items-center text-center mb-5">
          <hr className="flex-1 border-none h-[1px] bg-[#E8E8E8]" />
          <span className="py-0 px-[10px] text-xs border-[#E8E8E8] border rounded-md text-[#A4A4A4] bg-[#FAFAFA] p-1">
            OR
          </span>
          <hr className="flex-1 border-none h-[1px] bg-[#E8E8E8]" />
        </div>

        <InputField
          label="Email address"
          register={register}
          fieldName="email"
          fieldType="email"
          placeHolder="Enter your email address"
          isRequired={true}
          disabled={loggingIn}
          error={errors?.email?.message}
        />

        <InputField
          label="Password"
          register={register}
          fieldName="password"
          fieldType="password"
          placeHolder="Enter your password"
          isRequired={true}
          disabled={loggingIn}
          error={errors?.password?.message}
        />

        <div className="flex justify-between items-center mt-[-20px] mb-7">
          <div className="flex items-center gap-1">
            <InputField
              label=""
              register={register}
              fieldName="rememberMe"
              fieldType="checkbox"
              placeHolder=""
              isRequired={false}
              error={errors?.rememberMe?.message}
            />
            <span className="text-[#1A1A40] mb-1 max-sm:text-sm">
              Remember me
            </span>
          </div>

          <a className="text-primary max-sm:text-sm" href="/reset-password">
            Forgot password?
          </a>
        </div>

        <Button className="w-full h-[50px] mb-3" disabled={loggingIn}>
          Sign in
        </Button>

        <p className="sm:text-lg">
          Dont have an account?{" "}
          <a className="text-primary font-semibold" href="/signup/student">
            Sign up
          </a>
        </p>
      </form>
    </div>
  );
};

export default LoginPage;
