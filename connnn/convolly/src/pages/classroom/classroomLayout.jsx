import React, { useState, useRef, useEffect } from "react";
import AgoraRTC from "agora-rtc-sdk-ng";
import AC from "agora-chat";
import { APP_ID } from "../../../agora-config";
import { Button } from "@/components/button/button";
import classroomBackgroundImage from "@/assets/images/classroomBackgroundImage.png";
import tutor1 from "@/assets/images/tutor1.png";
import micOn from "@/assets/svgs/micOn.svg";
import micOff from "@/assets/svgs/micOff.svg";
import videoOn from "@/assets/svgs/videoOn.svg";
import videoOff from "@/assets/svgs/videoOff.svg";
import chat from "@/assets/svgs/chat.svg";
import send from "@/assets/svgs/send.svg";
import screen from "@/assets/svgs/shareScreen.svg";
import { X } from "lucide-react";
import "./style.css";

const ClassroomLayout = () => {
  const [joined, setJoined] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOn, setIsVideoOn] = useState(true);
  const [showChat, setShowChat] = useState(false);
  const [micIcon, setMicIcon] = useState(micOn);
  const [members, setMembers] = useState([]);
  const [chatLoggedIn, setChatLoggedIn] = useState(false);
  const [messages, setMessages] = useState([]);
  const [message, setMessage] = useState("");
  const [isScreenSharing, setIsScreenSharing] = useState(false);

  // --- Refs ---
  const rtcClient = useRef(null);
  const localAudioTrack = useRef(null);
  const localVideoTrack = useRef(null);
  const remoteUsers = useRef({});
  const conn = useRef(null);
  const screenTrack = useRef(null);

  const rtcUid = useRef(Math.floor(Math.random() * 2032));

  const VITE_AGORA_TEMP_TOKEN = import.meta.env.VITE_AGORA_TEMP_TOKEN;
  const VITE_AGORA_CHAT_TOKEN = import.meta.env.VITE_AGORA_CHAT_TOKEN;

  const token = VITE_AGORA_TEMP_TOKEN;
  const roomId = "test room";

  // Initialize Agora Chat connection once
  useEffect(() => {
    conn.current = new AC.connection({
      appKey: "411348949#1554976"
    });
  }, []);

  console.log(messages);

  const handleUserJoined = (user) => {
    console.log("user joined", user);
    console.log(members);

    setMembers((prev) => [
      ...prev,
      { uid: user.uid, name: `User ${user.uid}` }
    ]);
  };

  const handleUserPublished = async (user, mediaType) => {
    await rtcClient.current.subscribe(user, mediaType);

    if (mediaType === "video") {
      let remoteContainer = document.getElementById("remote-container");

      if (
        remoteContainer &&
        !document.getElementById(`remote-player-${user.uid}`)
      ) {
        const remoteVideoContainer = document.createElement("div");
        remoteVideoContainer.id = `remote-player-${user.uid}`;
        remoteVideoContainer.style.width = "180px";
        remoteVideoContainer.style.height = "135px";
        remoteVideoContainer.className =
          "rounded-md overflow-hidden border border-white shadow-md relative";
        remoteContainer.appendChild(remoteVideoContainer);
        user.videoTrack.play(remoteVideoContainer);
      }
    }

    if (mediaType === "audio") {
      user.audioTrack.play();
      updateMicStatus(user, true);
    }
    remoteUsers.current[user.uid] = user;
  };

  const handleUserUnpublished = (user, mediaType) => {
    if (mediaType === "video") {
      const el = document.getElementById(`remote-player-${user.uid}`);
      if (el) el.remove();
    }
    if (mediaType === "audio") {
      updateMicStatus(user, false);
    }
  };

  const handleUserLeft = (user) => {
    delete remoteUsers.current[user.uid];
    setMembers((prev) => prev.filter((m) => m.uid !== user.uid));
    const videoEl = document.getElementById(`remote-player-${user.uid}`);
    if (videoEl) videoEl.remove();
  };

  const toggleMic = async () => {
    const muted = !isMuted;
    setIsMuted(muted);
    setMicIcon(muted ? micOff : micOn);
    await localAudioTrack.current?.setMuted(muted);
  };

  const toggleVideo = async () => {
    const enabled = !isVideoOn;
    setIsVideoOn(enabled);
    if (localVideoTrack.current) {
      await localVideoTrack.current.setMuted(!enabled);
    }
  };

  const toggleUserMute = (uid) => {
    if (uid !== rtcUid.current) return;
    const muted = !isMuted;
    setIsMuted(muted);
    setMicIcon(muted ? micOff : micOn);
    localAudioTrack.current?.setMuted(muted);

    const micEl = document.querySelector(`#member-${uid} .mic-icon`);
    if (micEl) micEl.src = muted ? micOff : micOn;
  };

  const updateMicStatus = (user, isAudioOn) => {
    const micEl = document.querySelector(`#member-${user.uid} .mic-icon`);
    if (micEl) micEl.src = isAudioOn ? micOn : micOff;
  };

  const addSelfToMembers = () => {
    setMembers([{ uid: rtcUid.current, name: "You" }]);
  };

  const startVideoChat = async () => {
    rtcClient.current = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });

    rtcClient.current.on("user-joined", handleUserJoined);
    rtcClient.current.on("user-published", handleUserPublished);
    rtcClient.current.on("user-unpublished", handleUserUnpublished);
    rtcClient.current.on("user-left", handleUserLeft);

    const [audioTrack, videoTrack] =
      await AgoraRTC.createMicrophoneAndCameraTracks();

    localAudioTrack.current = audioTrack;
    localVideoTrack.current = videoTrack;

    try {
      await rtcClient.current.join(APP_ID, roomId, token, rtcUid.current);
      await rtcClient.current.publish([audioTrack, videoTrack]);

      addSelfToMembers();
      setJoined(true);

      // Agora Chat login
      const options = {
        user: "seyi123",
        accessToken: VITE_AGORA_CHAT_TOKEN
      };

      conn.current.addEventHandler("connection", {
        onConnected: () => setChatLoggedIn(true),
        onError: (error) => {
          console.error("Chat connection error:", error);
          setChatLoggedIn(false);
        },
        onTextMessage: (msg) => {
          console.log("msg", msg);
          setMessages((prev) => [
            ...prev,
            { senderId: msg.from, text: msg.msg }
          ]);
          // Handle presence message
          if (msg.msg === "introducing-myself" && msg.ext?.uid) {
            setMembers((prev) => {
              const exists = prev.some((m) => m.uid === msg.ext.uid);
              if (!exists) {
                return [...prev, { uid: msg.ext.uid, name: msg.ext.name }];
              }
              return prev;
            });
          }
        }
      });

      await conn.current.open(options);
      await conn.current.joinChatRoom({ roomId: "282513745444865" });

      // Send 'presence' message to room after joining
      conn.current.send({
        to: "282513745444865",
        chatType: "groupChat",
        msg: "introducing-myself",
        type: "txt",
        ext: {
          uid: rtcUid.current,
          name: `User ${rtcUid.current}`
        }
      });
    } catch (error) {
      console.log("error", error);
    }
  };

  const leaveRoom = async () => {
    localAudioTrack.current?.stop();
    localAudioTrack.current?.close();
    localVideoTrack.current?.stop();
    localVideoTrack.current?.close();

    await rtcClient.current?.unpublish();
    await rtcClient.current?.leave();

    setJoined(false);
    setMembers([]);
    setMessages([]);
  };

  const sendMessage = async (e) => {
    e.preventDefault();
    if (!message.trim() || !chatLoggedIn) return;

    const msg = AC.message.create({
      chatType: "groupChat",
      type: "txt",
      to: "282513745444865",
      msg: message
    });

    try {
      await conn.current.send(msg);
      setMessages((prev) => [...prev, { senderId: "You", text: message }]);
      setMessage("");
    } catch (e) {
      console.error("Send message error:", e);
    }
  };

  const toggleScreenShare = async () => {
    if (!isScreenSharing) {
      try {
        const [track] = await AgoraRTC.createScreenVideoTrack(
          { encoderConfig: "1080p_1" },
          "auto"
        );

        screenTrack.current = track;
        await rtcClient.current.unpublish(localVideoTrack.current);
        await rtcClient.current.publish(track);
        track.play("local-player");

        setIsScreenSharing(true);

        // Listen for when user stops screen sharing from browser UI
        track.on("track-ended", async () => {
          await rtcClient.current.unpublish(track);
          await rtcClient.current.publish(localVideoTrack.current);
          localVideoTrack.current.play("local-player");
          setIsScreenSharing(false);
        });
      } catch (error) {
        console.error("Screen sharing failed:", error);
      }
    } else {
      await rtcClient.current.unpublish(screenTrack.current);
      await rtcClient.current.publish(localVideoTrack.current);
      localVideoTrack.current.play("local-player");
      setIsScreenSharing(false);
    }
  };

  const enterRoom = async (e) => {
    e.preventDefault();
    await startVideoChat();
  };

  useEffect(() => {
    if (joined && localVideoTrack.current) {
      const tryPlay = () => {
        const el = document.getElementById("local-player");
        if (el) {
          localVideoTrack.current.play(el);
        } else {
          setTimeout(tryPlay, 100);
        }
      };
      tryPlay();
    }
  }, [joined]);

  return (
    <div>
      {!joined ? (
        <div className="lg:flex gap-8 items-center h-full w-full sm:p-8 p-4">
          <div className="lg:max-w-[65%] w-full overflow-hidden rounded-xl">
            <div className="relative">
              <div className="absolute inset-0 bg-black/40 z-0" />
              <img
                src={classroomBackgroundImage}
                alt="tutor background image"
                className="w-full h-full lg:max-h-[600px] max-md:max-h-[450px]"
              />
              <h3 className="absolute bottom-7 left-7 text-white sm:text-2xl text-xl font-bold">
                John Doe
              </h3>
            </div>

            <div className="flex gap-5 justify-center items-center bg-black text-white p-5">
              <div className="flex flex-col justify-center items-center">
                <img
                  id="mic-icon"
                  onClick={toggleMic}
                  src={micIcon}
                  alt="mic icon"
                  className="cursor-pointer max-w-6"
                />
                <p className="">Microphone</p>
              </div>

              <div className="flex flex-col justify-center items-center">
                <img
                  id="video-icon"
                  className="cursor-pointer max-w-6"
                  src={isVideoOn ? videoOn : videoOff}
                  onClick={toggleVideo}
                  alt="video icon"
                />
                <p className="">Video</p>
              </div>
            </div>
          </div>

          <form
            className="w-full lg:max-w-[35%] lg:p-10 max-lg:mt-7"
            id="form"
            onSubmit={enterRoom}
          >
            <h2 className="text-secondary sm:text-2xl text-xl font-bold text-center mb-2">
              English class with John
            </h2>
            <p className="text-[#4B5563] text-lg text-center mb-10">
              John is in this call
            </p>
            <Button type="submit" className="w-full">
              Join Now
            </Button>
          </form>
        </div>
      ) : (
        <div className="h-screen flex flex-col">
          <div
            className="absolute top-4 left-4 z-40 space-y-4"
            id="remote-container"
          ></div>

          <div
            id="local-player"
            className={`h-full bg-black relative transition-all duration-300
            ${showChat ? "w-[calc(100vw-350px)]" : "w-full"}
            `}
          >
            <div className="absolute right-5 top-1/2 transform -translate-y-1/2 rounded-l-lg sm:w-[200px] w-[150px] overflow-auto max-h-[80vh] z-50">
              {members.map((member) => (
                <div
                  key={member.uid}
                  className={`speaker user-rtc-${member.uid} shadow rounded-lg sm:p-5 p-3 flex flex-col items-center transition-all mb-3 w-full h-full max-h-[300px] bg-[#AF52DE] text-sm font-medium text-white`}
                  id={`member-${member.uid}`}
                >
                  <p className="mb-3 sm:text-lg">{member.name}</p>

                  <div className="relative">
                    <img
                      src={tutor1}
                      alt="user icon"
                      className="sm:w-20 w-14 sm:h-20 h-14 rounded-full object-cover"
                    />

                    {member.uid === rtcUid.current && (
                      <img
                        src={isMuted ? micOff : micOn}
                        alt="mute/unmute"
                        onClick={() => toggleUserMute(member.uid)}
                        className="absolute bottom-[-10px] right-[-10px]"
                      />
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {showChat && (
            <div className="absolute top-0 right-0 max-w-[350px] w-full h-[calc(100vh-100px)] bg-white shadow-md rounded-lg flex flex-col">
              <div className="p-2 border-b font-bold text-sm bg-gray-100 flex justify-between">
                <p>Chat</p>

                <X
                  className="w-5 h-5 cursor-pointer"
                  onClick={() => setShowChat(false)}
                />
              </div>

              <div className="flex-1 overflow-y-auto p-2 space-y-1 text-sm">
                {messages.map((msg, idx) => (
                  <div
                    key={idx}
                    className={`p-2 rounded w-fit ${
                      msg.senderId === "You"
                        ? "bg-[#1FC16B1A] text-secondary text-right ml-auto"
                        : "bg-[#EBEDF0] text-[#4B5563]"
                    }`}
                  >
                    {msg.text}
                  </div>
                ))}
              </div>

              <form
                onSubmit={sendMessage}
                className="border mt-auto flex flex-col"
              >
                <textarea
                  rows={4}
                  placeholder="Type a message..."
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  className="text-sm outline-none p-2 w-full resize-none"
                ></textarea>

                <div className="p-2">
                  <button
                    type="submit"
                    className="h-6 w-6 flex justify-center items-center ml-auto rounded-md bg-[#E8E8E8]"
                  >
                    <img id="send-icon" src={send} alt="send icon" />
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Controls Bar  */}
          <div className="w-full flex min-h-[100px] justify-center items-center bg-black p-5 text-white sm:gap-5 gap-14">
            <div className="flex flex-col justify-center items-center">
              <img
                id="mic-icon"
                onClick={toggleMic}
                src={micIcon}
                alt="mic icon"
                className="cursor-pointer max-w-6"
              />
              <p className="">Microphone</p>
            </div>

            <div className="flex flex-col justify-center items-center">
              <img
                id="video-icon"
                className="cursor-pointer max-w-6"
                src={isVideoOn ? videoOn : videoOff}
                alt="video icon"
                onClick={toggleVideo}
              />
              <p className="">Video</p>
            </div>

            <div className="flex flex-col justify-center items-center">
              <img
                id="chat-icon"
                className="cursor-pointer max-w-6"
                src={chat}
                alt="chat icon"
                onClick={() => setShowChat(!showChat)}
              />
              <p className="">Chat</p>
            </div>

            <div className="flex flex-col justify-center items-center">
              <img
                onClick={toggleScreenShare}
                id="screen-icon"
                className="cursor-pointer max-w-6"
                src={screen}
                alt="screen icon"
              />
              <p className="text-white">Share</p>
            </div>

            <button onClick={leaveRoom} className="text-red-500 font-bold">
              End Call
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClassroomLayout;
