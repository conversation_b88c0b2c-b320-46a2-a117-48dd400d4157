import React, { useState, useRef, useEffect } from "react";
import "./style.css";
import AgoraRTC from "agora-rtc-sdk-ng";
import { APP_ID } from "../../../agora-config";
import { Button } from "@/components/button/button";
import classroomBackgroundImage from "@/assets/images/classroomBackgroundImage.png";

function ClassroomLayout() {
  const [joined, setJoined] = useState(false);
  const [rtcClient, setRtcClient] = useState(null);
  const [rtcUid] = useState(() => Math.floor(Math.random() * 2032));
  const [remoteUsers, setRemoteUsers] = useState([]);
  const [isMuted, setIsMuted] = useState(false);

  const roomId = "main";
  const token = null;

  const membersRef = useRef(null);
  const rtcClientRef = useRef(null);
  const formRef = useRef(null);

  const localAudioTrackRef = useRef(null);
  const remoteAudioTracksRef = useRef({});

  const initRtc = async () => {
    const client = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });

    client.on("user-joined", handleUserJoined);
    rtcClientRef.current = client;
    setRtcClient(client);

    client.on("user-published", handleUserPublished);
    client.on("user-left", handleUserLeft);

    await client.join(APP_ID, roomId, token, rtcUid);
    const localAudioTrack = await AgoraRTC.createMicrophoneAudioTrack();

    client.enableAudioVolumeIndicator(); // Enable volume indication

    client.on("volume-indicator", (volumes) => {
      volumes.forEach((volume) => {
        const uid = volume.uid === rtcUid ? rtcUid : volume.uid;
        const el = document.getElementById(uid);
        if (el) {
          const indicator = el.querySelector(".speaking-indicator");
          if (volume.level > 40) {
            indicator?.classList.remove("hidden");
          } else {
            indicator?.classList.add("hidden");
          }
        }
      });
    });

    await client.publish(localAudioTrack);

    localAudioTrackRef.current = localAudioTrack;
    setJoined(true);

    // addMemberElement(rtcUid);
  };

  const handleUserJoined = async (user) => {
    console.log("joined", user);
    addMemberElement(user.uid);
  };

  const handleUserPublished = async (user, mediaType) => {
    const client = rtcClientRef.current;
    if (!client) return;

    await client.subscribe(user, mediaType);

    if (mediaType === "audio") {
      remoteAudioTracksRef.current[user.uid] = user.audioTrack;
      user.audioTrack.play();
    }
  };

  const handleUserLeft = async (user) => {
    delete remoteAudioTracksRef.current[user.uid];
    removeMemberElement(user.uid);
  };

  const addMemberElement = (uid) => {
    const isLocalUser = uid === rtcUid;

    const div = document.createElement("div");
    div.className =
      "shadow rounded-lg p-3 flex flex-col items-center transition-all mb-2 w-full h-full max-h-[300px] bg-red-300 text-sm font-medium text-gray-800";
    div.id = `${uid}`;
    div.innerHTML = `
      <div>
        <p>${uid === rtcUid ? "You" : "User " + uid}</p>
      </div>
      <div class="mt-1 text-xs font-medium text-green-600 hidden speaking-indicator">Speaking</div>
      <img
        src="${micOn}"
        alt="mic icon"
        class="mic-icon mt-2 cursor-pointer w-6 h-6 ${
          !isLocalUser ? "pointer-events-none opacity-50" : ""
        }"
        data-uid="${uid}"
      />
    `;
    membersRef.current?.appendChild(div);

    if (isLocalUser) {
      const micIcon = div.querySelector(".mic-icon");
      micIcon.addEventListener("click", toggleMute);
    }
  };

  const removeMemberElement = (uid) => {
    const el = document.getElementById(uid);
    if (el) el.remove();
  };

  const enterRoom = async (e) => {
    e.preventDefault();
    await initRtc();
  };

  const toggleMute = async () => {
    const audioTrack = localAudioTrackRef.current;
    if (!audioTrack) return;

    const micIcon = document.querySelector(`.mic-icon[data-uid="${rtcUid}"]`);
    if (!micIcon) return;

    if (isMuted) {
      await audioTrack.setEnabled(true);
      micIcon.src = micOn;
    } else {
      await audioTrack.setEnabled(false);
      micIcon.src = micOff;
    }

    setIsMuted(!isMuted);
  };

  const leaveRoom = async () => {
    if (localAudioTrackRef.current) {
      localAudioTrackRef.current.stop();
      localAudioTrackRef.current.close();
    }

    const micIcon = document.querySelector(`.mic-icon[data-uid="${rtcUid}"]`);
    if (micIcon) {
      micIcon.removeEventListener("click", toggleMute);
    }

    const client = rtcClientRef.current;
    if (client) {
      await client.unpublish();
      await client.leave();
    }

    rtcClientRef.current = null;
    setJoined(false);
    membersRef.current.innerHTML = "";
  };

    // const startVideoChat = async () => {
  //   // Read persisted values from localStorage (or default values)
  //   const persistedAudioMuted = JSON.parse(localStorage.getItem("muteAudio") || "true");
  //   const persistedVideoMuted = JSON.parse(localStorage.getItem("muteVideo") || "true");

  //   rtcClient.current = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });
  //   rtcClient.current.on("user-joined", handleUserJoined);
  //   rtcClient.current.on("user-published", handleUserPublished);
  //   rtcClient.current.on("user-unpublished", handleUserUnpublished);
  //   rtcClient.current.on("user-left", handleUserLeft);

  //   await rtcClient.current.join(APP_ID, roomId, token, rtcUid.current);

  //   const [audioTrack, videoTrack] = await AgoraRTC.createMicrophoneAndCameraTracks();

  //   // Apply mute states from localStorage
  //   audioTrack.setMuted(isMuted);
  //   videoTrack.setMuted(isVideoOn);

  //   localAudioTrack.current = audioTrack;
  //   localVideoTrack.current = videoTrack;
  //   localAudioTrackRef.current = audioTrack;
  //   localVideoTrackRef.current = videoTrack;

  //   await rtcClient.current.publish([audioTrack, videoTrack]);

  //   addSelfToMembers(); // manually add yourself
  //   setJoined(true);
  // };


  useEffect(() => {
    if (joined && membersRef.current) {
      addMemberElement(rtcUid);
    }
  }, [joined, membersRef]);

  return (
    <div>
      {!joined ? (
        <div className="lg:flex gap-8 items-center h-full w-full sm:p-8 p-4">
          <div className="lg:max-w-[65%] w-full overflow-hidden rounded-xl">
            <div className="relative">
              <div className="absolute inset-0 bg-black/40 z-0" />

              <img
                src={classroomBackgroundImage}
                alt="tutor background image"
                className="w-full h-full max-h-[550px]"
              />

              <h3 className="absolute bottom-7 left-7 text-white sm:text-2xl text-xl font-bold">
                John Doe
              </h3>
            </div>

            <div className="flex gap-5 justify-center items-center bg-black text-white p-5">
              <div className="flex flex-col justify-center items-center">
                <img
                  id="mic-icon"
                  src={micOn}
                  alt="mic icon"
                  className="cursor-pointer max-w-6"
                />

                <p className="">Microphone</p>
              </div>

              <div className="flex flex-col justify-center items-center">
                <img
                  id="video-icon"
                  className="cursor-pointer max-w-6"
                  src={videoOn}
                  alt="video icon"
                />

                <p className="">Video</p>
              </div>
            </div>
          </div>

          <form
            className="w-full lg:max-w-[35%] lg:p-10"
            id="form"
            ref={formRef}
            onSubmit={enterRoom}
          >
            <h2 className="text-secondary sm:text-2xl text-xl font-bold text-center mb-2">
              English class with John
            </h2>
            <p className="text-[#4B5563] text-lg text-center mb-10">
              John is in this call
            </p>
            <Button type="submit" className="w-full">
              Join Now
            </Button>
          </form>
        </div>
      ) : (
        <div
          id="members"
          ref={membersRef}
          className="absolute right-0 top-1/2 transform -translate-y-1/2 shadow-xl rounded-l-lg p-4 w-64 bg-transparent overflow-auto max-h-[80vh]"
        ></div>
      )}
    </div>
  );
}

export default ClassroomLayout;
