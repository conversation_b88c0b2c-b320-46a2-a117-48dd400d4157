import React from "react";
import { Clock, Calendar } from "lucide-react";

/**
 * Compact availability display component for tutor cards
 * Shows next available time slots in a clean, clickable format
 */
const AvailabilityDisplay = ({ 
  availabilityData, 
  onSlotClick, 
  isLoading = false,
  compact = false 
}) => {
  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-gray-500">
        <Clock className="w-4 h-4 animate-pulse" />
        <span className="text-sm">Loading availability...</span>
      </div>
    );
  }

  if (!availabilityData?.hasAvailability) {
    return (
      <div className="flex items-center gap-2 text-gray-500">
        <Clock className="w-4 h-4" />
        <span className="text-sm">{availabilityData?.message || "No upcoming availability"}</span>
      </div>
    );
  }

  const { slots, message, totalAvailable } = availabilityData;

  if (compact) {
    // Compact view - just show status and first slot
    const firstSlot = slots[0];
    return (
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1 text-green-600">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span className="text-sm font-medium">Available</span>
        </div>
        {firstSlot && (
          <button
            onClick={() => onSlotClick?.(firstSlot)}
            className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
          >
            {firstSlot.displayTime}
          </button>
        )}
        {totalAvailable > 1 && (
          <span className="text-xs text-gray-500">+{totalAvailable - 1} more</span>
        )}
      </div>
    );
  }

  // Full view - show multiple slots
  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <Calendar className="w-4 h-4 text-green-600" />
        <span className="text-sm font-medium text-gray-700">Next available times:</span>
      </div>
      
      <div className="flex flex-wrap gap-2">
        {slots.map((slot, index) => (
          <button
            key={index}
            onClick={() => onSlotClick?.(slot)}
            className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-blue-700 bg-blue-50 border border-blue-200 rounded-full hover:bg-blue-100 hover:border-blue-300 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
          >
            <Clock className="w-3 h-3 mr-1" />
            {slot.fullDisplay}
          </button>
        ))}
      </div>

      {message && (
        <div className="text-xs text-gray-500 mt-1">
          {message}
        </div>
      )}
    </div>
  );
};

/**
 * Simple availability status indicator
 * Shows just the availability status without detailed slots
 */
export const AvailabilityStatus = ({ 
  statusText, 
  isAvailable = false,
  onClick 
}) => {
  return (
    <div 
      className={`flex items-center gap-2 ${onClick ? 'cursor-pointer hover:opacity-80' : ''}`}
      onClick={onClick}
    >
      <div className={`w-2 h-2 rounded-full ${
        isAvailable ? 'bg-green-500' : 'bg-yellow-500'
      }`}></div>
      <span className={`text-sm ${
        isAvailable ? 'text-green-700' : 'text-yellow-700'
      }`}>
        {statusText}
      </span>
    </div>
  );
};

/**
 * Availability badge for quick status display
 */
export const AvailabilityBadge = ({ 
  isAvailable, 
  text,
  onClick 
}) => {
  return (
    <span 
      className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
        isAvailable 
          ? 'bg-green-100 text-green-800 border border-green-200' 
          : 'bg-yellow-100 text-yellow-800 border border-yellow-200'
      } ${onClick ? 'cursor-pointer hover:opacity-80' : ''}`}
      onClick={onClick}
    >
      <div className={`w-1.5 h-1.5 rounded-full mr-1.5 ${
        isAvailable ? 'bg-green-500' : 'bg-yellow-500'
      }`}></div>
      {text}
    </span>
  );
};

/**
 * Quick booking slots - horizontal scrollable list
 */
export const QuickBookingSlots = ({ 
  slots = [], 
  onSlotClick,
  maxVisible = 3 
}) => {
  if (!slots || slots.length === 0) {
    return null;
  }

  const visibleSlots = slots.slice(0, maxVisible);
  const hasMore = slots.length > maxVisible;

  return (
    <div className="space-y-2">
      <div className="text-xs font-medium text-gray-600 uppercase tracking-wide">
        Quick Book
      </div>
      <div className="flex gap-2 overflow-x-auto pb-1">
        {visibleSlots.map((slot, index) => (
          <button
            key={index}
            onClick={() => onSlotClick?.(slot)}
            className="flex-shrink-0 px-3 py-2 text-xs font-medium text-white bg-primary rounded-lg hover:bg-primary-dark transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-1"
          >
            <div className="text-center">
              <div className="font-semibold">{slot.dayName}</div>
              <div className="text-xs opacity-90">{slot.displayTime}</div>
            </div>
          </button>
        ))}
        {hasMore && (
          <button
            onClick={() => onSlotClick?.({ showAll: true })}
            className="flex-shrink-0 px-3 py-2 text-xs font-medium text-primary border border-primary rounded-lg hover:bg-primary hover:text-white transition-colors duration-200"
          >
            +{slots.length - maxVisible}
          </button>
        )}
      </div>
    </div>
  );
};

export default AvailabilityDisplay;
