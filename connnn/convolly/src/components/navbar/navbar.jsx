import React, { useState } from "react";
import { Button } from "../button/button";
import { ChevronDownIcon, MenuIcon, XIcon } from "lucide-react";
import logo from "../../assets/svgs/logo.svg";
import { useNavigate } from "react-router-dom";

const Navbar = () => {
  const [isMobileNavOpen, setIsMobileNavOpen] = useState(false);
  const navigate = useNavigate();

  const navItems = [
    { label: "Find Tutors", href: "/find-tutors" },
    { label: "How It Works", href: "#" },
    { label: "Become a Tutor", href: "/signup/tutor" }
  ];

  return (
    <div>
      <header className="flex w-full items-center justify-between sm:px-8 px-3 py-5">
        <a className="relative sm:w-[190px] w-[100px] h-[50px]" href="/">
          <span className="sr-only">Go to Convolly homepage</span>
          <img
            src={logo}
            alt="Convolly logo"
            className="w-full h-full cursor-pointer"
          />
        </a>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex flex-1 justify-center mx-8">
          <div className="flex justify-center gap-6">
            {navItems.map((item, index) => (
              <a
                key={index}
                href={item.href}
                className="font-medium text-black sm:text-lg text-base text-center tracking-normal leading-6 whitespace-nowrap"
              >
                {item.label}
              </a>
            ))}
          </div>
        </div>

        {/* Desktop Actions */}
        <div className="hidden lg:flex items-center gap-6">
          <div className="flex items-center gap-[3px]">
            <span className="font-medium text-black text-base">EN</span>
            <ChevronDownIcon className="w-5 h-5" />
          </div>

          <Button
            variant="ghost"
            className="p-0 font-bold text-black text-xl"
            onClick={() => navigate("/signin")}
          >
            Login
          </Button>

          <Button className="w-[132px] h-[45px] text-[20px]" onClick={() => navigate("/signup/student")}>
            Sign Up
          </Button>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="lg:hidden"
          onClick={() => setIsMobileNavOpen(!isMobileNavOpen)}
          aria-label="Toggle mobile menu"
        >
          {isMobileNavOpen ? (
            <XIcon className="w-6 h-6" />
          ) : (
            <MenuIcon className="w-6 h-6" />
          )}
        </button>
      </header>

      {/* Mobile Navigation */}
      <div
        className={`
          lg:hidden px-5 pb-5 absolute bg-white top-100 left-0 right-0 z-50 overflow-hidden transition-all duration-300 ease-in-out
          ${
            isMobileNavOpen
              ? "opacity-100 scale-100"
              : "max-h-0 opacity-0 scale-95"
          }
        `}
      >
        {navItems.map((item, index) => (
          <a
            key={index}
            href={item.href}
            className={`block text-xl text-black py-4 font-bold text-center ${
              index === 0 && "border-t-[1px]"
            }`}
            onClick={() => setIsMobileNavOpen(false)}
          >
            {item.label}
          </a>
        ))}

        <Button className="flex-1 w-full my-4" onClick={() => navigate("/signup/student")}>
          Sign Up
        </Button>

        <Button
          variant="ghost"
          className="p-0 font-bold text-black text-xl text-center w-full mb-4"
          onClick={() => navigate("/signin")}
        >
          Login
        </Button>

        <div className="flex items-center justify-center gap-[3px] w-full">
          <span className="font-bold text-black text-xl">EN</span>
          <ChevronDownIcon className="w-5 h-5" />
        </div>
      </div>
    </div>
  );
};

export default Navbar;
