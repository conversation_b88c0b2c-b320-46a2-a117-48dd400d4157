import React, { useState } from "react";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";

// API hooks
import { useUpdateStudentProfileSettingsMutation } from "@/redux/slices/student/studentSettingsApiSlice";
import { useUpdateTutorProfileSettingsMutation } from "@/redux/slices/tutor/tutorSettingsApiSlice";
import { useUploadProfileImageMutation, useGetProfileImageQuery } from "@/redux/slices/student/profileImageApiSlice";

// Components
import { Button } from "@/components/button/button";

/**
 * Test component to verify API endpoints are working correctly
 * This component tests:
 * 1. Student profile update: PUT /api/profile/students/{studentId}
 * 2. Tutor profile update: PUT /api/profile/tutors/{tutorId}
 * 3. Profile image upload: POST /api/profile-image/upload
 * 4. Profile image fetch: GET /api/profile-image
 */
const ProfileSettingsTest = () => {
  const user = useSelector((state) => state?.app?.userInfo?.user);
  const [testResults, setTestResults] = useState({});
  const [isRunning, setIsRunning] = useState(false);

  // API hooks
  const [updateStudentProfile] = useUpdateStudentProfileSettingsMutation();
  const [updateTutorProfile] = useUpdateTutorProfileSettingsMutation();
  const [uploadProfileImage] = useUploadProfileImageMutation();
  const { data: profileImageData, refetch: refetchProfileImage } = useGetProfileImageQuery();

  const addTestResult = (testName, success, message, data = null) => {
    setTestResults(prev => ({
      ...prev,
      [testName]: {
        success,
        message,
        data,
        timestamp: new Date().toISOString()
      }
    }));
  };

  // Test student profile update
  const testStudentProfileUpdate = async () => {
    if (user?.role !== "student") {
      addTestResult("studentProfile", false, "User is not a student");
      return;
    }

    try {
      const testData = {
        studentId: user.id,
        firstname: user.firstname,
        lastname: user.lastname,
        phone: user.phone || "+1234567890",
        timeZone: user.timeZone || "America/New_York",
      };

      const response = await updateStudentProfile(testData).unwrap();
      addTestResult("studentProfile", true, "Student profile update successful", response);
      toast.success("Student profile test passed!");
    } catch (error) {
      addTestResult("studentProfile", false, `Student profile update failed: ${error.data?.message || error.message}`, error);
      toast.error("Student profile test failed!");
    }
  };

  // Test tutor profile update
  const testTutorProfileUpdate = async () => {
    if (user?.role !== "tutor") {
      addTestResult("tutorProfile", false, "User is not a tutor");
      return;
    }

    try {
      const testData = {
        tutorId: user.id,
        firstname: user.firstname,
        lastname: user.lastname,
        phone: user.phone || "+1234567890",
        timeZone: user.timeZone || "America/New_York",
        aboutMe: user.aboutMe || "Test about me section",
      };

      const response = await updateTutorProfile(testData).unwrap();
      addTestResult("tutorProfile", true, "Tutor profile update successful", response);
      toast.success("Tutor profile test passed!");
    } catch (error) {
      addTestResult("tutorProfile", false, `Tutor profile update failed: ${error.data?.message || error.message}`, error);
      toast.error("Tutor profile test failed!");
    }
  };

  // Test profile image upload
  const testProfileImageUpload = async () => {
    try {
      // Create a small test image (1x1 pixel PNG)
      const testImageBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";

      const response = await uploadProfileImage({
        imageBase64: testImageBase64
      }).unwrap();

      addTestResult("profileImageUpload", true, "Profile image upload successful", response);
      toast.success("Profile image upload test passed!");
      
      // Refetch to test the GET endpoint
      refetchProfileImage();
    } catch (error) {
      addTestResult("profileImageUpload", false, `Profile image upload failed: ${error.data?.message || error.message}`, error);
      toast.error("Profile image upload test failed!");
    }
  };

  // Test profile image fetch
  const testProfileImageFetch = async () => {
    try {
      const response = await refetchProfileImage();
      addTestResult("profileImageFetch", true, "Profile image fetch successful", response.data);
      toast.success("Profile image fetch test passed!");
    } catch (error) {
      addTestResult("profileImageFetch", false, `Profile image fetch failed: ${error.data?.message || error.message}`, error);
      toast.error("Profile image fetch test failed!");
    }
  };

  // Run all applicable tests
  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults({});

    try {
      // Test profile updates based on user role
      if (user?.role === "student") {
        await testStudentProfileUpdate();
      } else if (user?.role === "tutor") {
        await testTutorProfileUpdate();
      }

      // Test profile image endpoints
      await testProfileImageUpload();
      await testProfileImageFetch();

    } catch (error) {
      console.error("Error running tests:", error);
    } finally {
      setIsRunning(false);
    }
  };

  if (!user) {
    return (
      <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">Please log in to test the profile settings endpoints.</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-2xl font-bold mb-4">Profile Settings API Test</h2>
        
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-semibold text-blue-800 mb-2">User Information:</h3>
          <p className="text-blue-700">Role: {user.role}</p>
          <p className="text-blue-700">ID: {user.id}</p>
          <p className="text-blue-700">Name: {user.firstname} {user.lastname}</p>
        </div>

        <div className="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h3 className="font-semibold text-gray-800 mb-2">Endpoints to Test:</h3>
          <ul className="text-gray-700 text-sm space-y-1">
            {user.role === "student" && (
              <li>• Student Profile: PUT /api/profile/students/{user.id}</li>
            )}
            {user.role === "tutor" && (
              <li>• Tutor Profile: PUT /api/profile/tutors/{user.id}</li>
            )}
            <li>• Profile Image Upload: POST /api/profile-image/upload</li>
            <li>• Profile Image Fetch: GET /api/profile-image</li>
          </ul>
        </div>

        <div className="mb-6">
          <Button
            onClick={runAllTests}
            disabled={isRunning}
            className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            {isRunning ? "Running Tests..." : "Run All Tests"}
          </Button>
        </div>

        {/* Test Results */}
        {Object.keys(testResults).length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Test Results:</h3>
            
            {Object.entries(testResults).map(([testName, result]) => (
              <div
                key={testName}
                className={`p-4 rounded-lg border ${
                  result.success
                    ? "bg-green-50 border-green-200"
                    : "bg-red-50 border-red-200"
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className={`font-semibold ${
                    result.success ? "text-green-800" : "text-red-800"
                  }`}>
                    {testName} {result.success ? "✅" : "❌"}
                  </h4>
                  <span className="text-xs text-gray-500">
                    {new Date(result.timestamp).toLocaleTimeString()}
                  </span>
                </div>
                
                <p className={`text-sm ${
                  result.success ? "text-green-700" : "text-red-700"
                }`}>
                  {result.message}
                </p>
                
                {result.data && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-xs text-gray-600">
                      View Response Data
                    </summary>
                    <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-x-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Current Profile Image Data */}
        {profileImageData && (
          <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <h3 className="font-semibold text-gray-800 mb-2">Current Profile Image Data:</h3>
            <pre className="text-xs bg-white p-2 rounded border overflow-x-auto">
              {JSON.stringify(profileImageData, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProfileSettingsTest;
