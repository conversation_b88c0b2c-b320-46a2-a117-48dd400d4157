import React, { useState } from "react";
import { useSelector } from "react-redux";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";

// Import the API hooks
import { useUpdateStudentProfileSettingsMutation } from "@/redux/slices/student/studentSettingsApiSlice";
import { useUpdateTutorProfileSettingsMutation } from "@/redux/slices/tutor/tutorSettingsApiSlice";

// Import utilities
import {
  updateStudentProfileSettings,
  updateTutorProfileSettings,
  validateStudentProfileData,
  validateTutorProfileData,
  formatProfileDataForAPI,
} from "@/utils/profileSettingsUtils";

/**
 * Example component demonstrating how to use the profile settings endpoints:
 * - Student Profile Settings: /api/profile/students/{studentId}
 * - Tutor Profile Settings: /api/profile/tutors/{tutorId}
 */
const ProfileSettingsExample = () => {
  const user = useSelector((state) => state?.app?.userInfo?.user);
  const [isLoading, setIsLoading] = useState(false);

  // API hooks
  const [updateStudentProfile] = useUpdateStudentProfileSettingsMutation();
  const [updateTutorProfile] = useUpdateTutorProfileSettingsMutation();

  // Form handling
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm();

  /**
   * Handle Student Profile Update
   * Uses endpoint: PUT /api/profile/students/{studentId}
   */
  const handleStudentProfileUpdate = async (formData) => {
    try {
      setIsLoading(true);

      // Validate data
      const validation = validateStudentProfileData(formData);
      if (!validation.isValid) {
        Object.values(validation.errors).forEach(error => {
          toast.error(error);
        });
        return;
      }

      // Format data for API
      const formattedData = formatProfileDataForAPI(formData, "student");

      // Update profile using utility function
      const response = await updateStudentProfileSettings(
        updateStudentProfile,
        user.id,
        formattedData
      );

      console.log("Student profile updated:", response);
      
      // Reset form or redirect as needed
      // reset();
      
    } catch (error) {
      console.error("Failed to update student profile:", error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Handle Tutor Profile Update
   * Uses endpoint: PUT /api/profile/tutors/{tutorId}
   */
  const handleTutorProfileUpdate = async (formData) => {
    try {
      setIsLoading(true);

      // Validate data
      const validation = validateTutorProfileData(formData);
      if (!validation.isValid) {
        Object.values(validation.errors).forEach(error => {
          toast.error(error);
        });
        return;
      }

      // Format data for API
      const formattedData = formatProfileDataForAPI(formData, "tutor");

      // Update profile using utility function
      const response = await updateTutorProfileSettings(
        updateTutorProfile,
        user.id,
        formattedData
      );

      console.log("Tutor profile updated:", response);
      
      // Reset form or redirect as needed
      // reset();
      
    } catch (error) {
      console.error("Failed to update tutor profile:", error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Direct API usage examples (without utility functions)
   */
  const directStudentProfileUpdate = async (profileData) => {
    try {
      const response = await updateStudentProfile({
        studentId: user.id,
        ...profileData,
      }).unwrap();

      toast.success("Student profile updated successfully!");
      return response;
    } catch (error) {
      toast.error("Failed to update student profile");
      throw error;
    }
  };

  const directTutorProfileUpdate = async (profileData) => {
    try {
      const response = await updateTutorProfile({
        tutorId: user.id,
        ...profileData,
      }).unwrap();

      toast.success("Tutor profile updated successfully!");
      return response;
    } catch (error) {
      toast.error("Failed to update tutor profile");
      throw error;
    }
  };

  // Determine which form to show based on user role
  const isStudent = user?.role === "student";
  const isTutor = user?.role === "tutor";

  if (!user) {
    return <div>Please log in to access profile settings.</div>;
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      <h1 className="text-2xl font-bold mb-6">
        Profile Settings - {isStudent ? "Student" : "Tutor"}
      </h1>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h2 className="font-semibold text-blue-800 mb-2">API Endpoints Used:</h2>
        <ul className="text-blue-700 text-sm">
          {isStudent && (
            <li>• Student Profile: PUT /api/profile/students/{user.id}</li>
          )}
          {isTutor && (
            <li>• Tutor Profile: PUT /api/profile/tutors/{user.id}</li>
          )}
        </ul>
      </div>

      <form
        onSubmit={handleSubmit(
          isStudent ? handleStudentProfileUpdate : handleTutorProfileUpdate
        )}
        className="space-y-4"
      >
        {/* Common fields for both student and tutor */}
        <div>
          <label className="block text-sm font-medium mb-1">First Name</label>
          <input
            type="text"
            {...register("firstname", { required: "First name is required" })}
            className="w-full border rounded-md px-3 py-2"
            defaultValue={user.firstname}
          />
          {errors.firstname && (
            <p className="text-red-500 text-sm mt-1">{errors.firstname.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Last Name</label>
          <input
            type="text"
            {...register("lastname", { required: "Last name is required" })}
            className="w-full border rounded-md px-3 py-2"
            defaultValue={user.lastname}
          />
          {errors.lastname && (
            <p className="text-red-500 text-sm mt-1">{errors.lastname.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">Phone</label>
          <input
            type="tel"
            {...register("phone")}
            className="w-full border rounded-md px-3 py-2"
            defaultValue={user.phone}
          />
        </div>

        {/* Tutor-specific fields */}
        {isTutor && (
          <>
            <div>
              <label className="block text-sm font-medium mb-1">About Me</label>
              <textarea
                {...register("aboutMe")}
                className="w-full border rounded-md px-3 py-2 h-24"
                defaultValue={user.aboutMe}
                placeholder="Tell students about yourself..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Headline</label>
              <input
                type="text"
                {...register("headline")}
                className="w-full border rounded-md px-3 py-2"
                defaultValue={user.headline}
                placeholder="Your professional headline..."
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Base Price (USD)</label>
              <input
                type="number"
                {...register("basePrice", { min: 1 })}
                className="w-full border rounded-md px-3 py-2"
                defaultValue={user.basePrice}
                placeholder="50"
              />
            </div>
          </>
        )}

        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isLoading ? "Updating..." : "Update Profile"}
        </button>
      </form>

      {/* Example usage documentation */}
      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-semibold mb-2">Usage Examples:</h3>
        <pre className="text-xs bg-white p-2 rounded border overflow-x-auto">
{`// Student Profile Update
const updateStudent = async () => {
  await updateStudentProfile({
    studentId: "${user.id}",
    firstname: "John",
    lastname: "Doe",
    phone: "+1234567890",
    timeZone: "America/New_York"
  }).unwrap();
};

// Tutor Profile Update
const updateTutor = async () => {
  await updateTutorProfile({
    tutorId: "${user.id}",
    firstname: "Jane",
    lastname: "Smith",
    aboutMe: "Experienced tutor...",
    basePrice: 50,
    languages: [{ name: "English", level: "Native" }]
  }).unwrap();
};`}
        </pre>
      </div>
    </div>
  );
};

export default ProfileSettingsExample;
