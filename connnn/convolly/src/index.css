@tailwind base;
@tailwind components;
@tailwind utilities;

.custom-checkbox {
	@apply appearance-none w-5 h-5 bg-gray-100 border border-gray-400 rounded-sm relative cursor-pointer transition duration-200 ease-in-out;
}

.custom-checkbox:checked {
  background-color: #fff;
  border-color: #3bb273;
}

.custom-checkbox:checked::after {
  content: "";
  position: absolute;
  top: 3px;
  left: 7px;
  width: 4px;
  height: 9px;
  border: solid #3bb273;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
  transition: all 0.25s ease-in-out;
}

.nsm7Bb-HzV7m-LgbsSe {
  -webkit-border-radius: 4px;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-transition: none !important;
  transition: none !important;
  -webkit-user-select: none;
  -webkit-appearance: none;
  background-color: #fff;
  background-image: none;
  /* border: 1px solid #dadce0; */
  border: none;
  color: #121212;
  cursor: pointer;
  font-size: 16px;
  /* height: 40px; */
  letter-spacing: 0.25px;
  outline: none;
  overflow: hidden;
  padding: 0px 12px !important;
  position: relative;
  /* text-align: center;
  vertical-align: middle; */
  white-space: nowrap;
  width: 100% !important;
}

.nsm7Bb-HzV7m-LgbsSe.JGcpL-RbRzK {
  -webkit-border-radius: none;
  border-radius: none;
  width: 100% !important;
}

.nsm7Bb-HzV7m-LgbsSe.hJDwNd-SxQuSe.i5vt6e-Ia7Qfc.JGcpL-RbRzK {
  width: 100% !important;
}

/*enforcing some style on the phone input component*/
.PhoneInputInput {
  border: none;
  outline: none;
}


/* Remove up and down arrows for number inputs */
.no-arrows::-webkit-inner-spin-button,
.no-arrows::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.no-arrows {
  -moz-appearance: textfield;
}

.smooth-scroll-slider .swiper-wrapper {
	animation: scroll-left 20s linear infinite;
	display: flex;
	width: max-content;
}

@keyframes scroll-left {
	0% {
		transform: translateX(0);
	}
	100% {
		transform: translateX(-50%);
	}
}


.shadow-around {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.shadow-light-around {
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);

}