import LoginPage from "../pages/auth/login";
import CreatePassword from "../pages/auth/passwordManager/CreatePassword";
import ForgotPassword from "../pages/auth/passwordManager/ForgotPassword";
import VerifyPassword from "../pages/auth/passwordManager/VerifyPassword";
import { HomePage } from "../pages/home/<USER>";
import SignupPage from "../pages/auth/signup";
import FindTutors from "../pages/findTutors/findTutors";
import TutorOnboarding from "../pages/tutor/tutorOnboarding/tutorOnboardingLayout";
import StudentOnboarding from "@/pages/student/studentOnboarding/studentOnboardingLayout";
import StudentDashboard from "@/pages/student/studentDashboard/studentDashboard";
import TutorDashboard from "@/pages/tutor/tutorDashboard/tutorDashboard";
import ClassroomLayout from "@/pages/classroom/classroomLayout";
import Classroom from "@/pages/classroom/classroom";
import TutorProfile from "@/pages/tutor/tutorProfile/tutorProfile";
import Payments from "@/pages/payments/Payments";
import PostPayment from "@/pages/payments/PostPayment";
import LessonTrial from "@/pages/lessonTrial/LessonTrial";

export const appRoutes = [
  {
    path: "/",
    element: <HomePage />
  },
  {
    path: "/signin",
    element: <LoginPage />
  },
  {
    path: "/reset-password",
    element: <ForgotPassword />
  },
  {
    path: "create-password",
    element: <CreatePassword />
  },
  {
    path: "/verify-password/",
    element: <VerifyPassword />
  },
  {
    path: "/signup/:role",
    element: <SignupPage />
  },
  {
    path: "/find-tutors",
    element: <FindTutors />
  },
  {
    path: "/tutor-onboarding",
    element: <TutorOnboarding />
  },
  {
    path: "/tutor-profile/:id",
    element: <TutorProfile />
  },
  {
    path: "/tutor-dashboard",
    element: <TutorDashboard />
  },
  {
    path: "/classroom",
    element: <ClassroomLayout />
  },
  {
    path: "/student-onboarding",
    element: <StudentOnboarding />
  },
  {
    path: "/student-dashboard",
    element: <StudentDashboard />
  },
  {
    path: "/tutor-dashboard",
    element: <TutorDashboard />
  },
  {
    path: "/payment",
    element: <Payments />
  },
  {
    path: "/post-payment",
    element: <PostPayment />
  },
  {
    path: "/trial-lesson",
    element: <LessonTrial />
  }
];
