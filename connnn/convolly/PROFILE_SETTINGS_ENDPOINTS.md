# Profile Settings Endpoints Documentation

This document explains how to use the profile settings endpoints for both students and tutors in the Convolly application.

## Endpoints

### Student Profile Settings
- **Endpoint**: `PUT /api/profile/students/{studentId}`
- **Purpose**: Update student profile information
- **Authentication**: Required (student role)

### Tutor Profile Settings
- **Endpoint**: `PUT /api/profile/tutors/{tutorId}`
- **Purpose**: Update tutor profile information
- **Authentication**: Required (tutor role)

## Frontend Implementation

### API Slices

#### Student Settings API Slice
```javascript
// File: src/redux/slices/student/studentSettingsApiSlice.js
import { useUpdateStudentProfileSettingsMutation } from "@/redux/slices/student/studentSettingsApiSlice";

const [updateStudentProfile] = useUpdateStudentProfileSettingsMutation();
```

#### Tutor Settings API Slice
```javascript
// File: src/redux/slices/tutor/tutorSettingsApiSlice.js
import { useUpdateTutorProfileSettingsMutation } from "@/redux/slices/tutor/tutorSettingsApiSlice";

const [updateTutorProfile] = useUpdateTutorProfileSettingsMutation();
```

### Usage Examples

#### Student Profile Update
```javascript
const updateStudentProfile = async (studentData) => {
  try {
    const response = await updateStudentProfile({
      studentId: user.id,
      firstname: "John",
      lastname: "Doe",
      phone: "+1234567890",
      timeZone: "America/New_York",
      countryOfBirth: "United States"
    }).unwrap();
    
    console.log("Student profile updated:", response);
  } catch (error) {
    console.error("Error updating student profile:", error);
  }
};
```

#### Tutor Profile Update
```javascript
const updateTutorProfile = async (tutorData) => {
  try {
    const response = await updateTutorProfile({
      tutorId: user.id,
      firstname: "Jane",
      lastname: "Smith",
      phone: "+1234567890",
      timeZone: "America/New_York",
      countryOfBirth: "United States",
      languages: [
        { name: "English", level: "Native" },
        { name: "Spanish", level: "Fluent" }
      ],
      teachingSubjects: [
        { title: "Mathematics" },
        { title: "Physics" }
      ],
      aboutMe: "Experienced tutor with 5+ years of teaching experience...",
      headline: "Expert Math and Physics Tutor",
      basePrice: 50,
      currency: "USD"
    }).unwrap();
    
    console.log("Tutor profile updated:", response);
  } catch (error) {
    console.error("Error updating tutor profile:", error);
  }
};
```

## Data Structures

### Student Profile Data
```javascript
const studentProfileData = {
  firstname: string,           // Required
  lastname: string,            // Required
  phone: string,               // Optional
  timeZone: string,            // Required
  countryOfBirth: string,      // Optional
  // Add other student-specific fields as needed
};
```

### Tutor Profile Data
```javascript
const tutorProfileData = {
  // Basic Information
  firstname: string,           // Required
  lastname: string,            // Required
  phone: string,               // Optional
  timeZone: string,            // Required
  countryOfBirth: string,      // Optional
  
  // Professional Information
  languages: [                 // Required
    {
      name: string,            // Language name
      level: string            // Proficiency level
    }
  ],
  teachingSubjects: [          // Required
    {
      title: string            // Subject title
    }
  ],
  
  // Profile Content
  aboutMe: string,             // Optional
  headline: string,            // Optional
  motivatePotentialStudent: string, // Optional
  teachingExperience: string,  // Optional
  
  // Pricing
  basePrice: number,           // Optional (in dollars)
  currency: string,            // Optional (default: "USD")
  
  // Media
  introVideo: string,          // Optional (base64 or URL)
  image: string,               // Optional (base64 or URL)
  
  // Credentials
  certificates: [              // Optional
    {
      title: string,
      institution: string,
      year: string
    }
  ]
};
```

## Component Integration

### Using in React Components

```javascript
import React from "react";
import { useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import { useUpdateStudentProfileSettingsMutation } from "@/redux/slices/student/studentSettingsApiSlice";
import { useUpdateTutorProfileSettingsMutation } from "@/redux/slices/tutor/tutorSettingsApiSlice";

const ProfileSettings = () => {
  const user = useSelector((state) => state?.app?.userInfo?.user);
  const [updateStudentProfile] = useUpdateStudentProfileSettingsMutation();
  const [updateTutorProfile] = useUpdateTutorProfileSettingsMutation();
  
  const { register, handleSubmit } = useForm();
  
  const onSubmit = async (data) => {
    if (user.role === "student") {
      await updateStudentProfile({
        studentId: user.id,
        ...data
      });
    } else if (user.role === "tutor") {
      await updateTutorProfile({
        tutorId: user.id,
        ...data
      });
    }
  };
  
  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      {/* Form fields */}
    </form>
  );
};
```

## Error Handling

### Common Error Responses
- `400 Bad Request`: Invalid data format or missing required fields
- `401 Unauthorized`: User not authenticated
- `403 Forbidden`: User doesn't have permission to update this profile
- `404 Not Found`: Profile not found
- `500 Internal Server Error`: Server error

### Error Handling Example
```javascript
try {
  const response = await updateStudentProfile(data).unwrap();
  toast.success("Profile updated successfully!");
} catch (error) {
  if (error.status === 400) {
    toast.error("Please check your input data");
  } else if (error.status === 401) {
    toast.error("Please log in again");
  } else {
    toast.error("Failed to update profile");
  }
}
```

## Validation

### Client-side Validation
Use the utility functions provided in `src/utils/profileSettingsUtils.js`:

```javascript
import { validateStudentProfileData, validateTutorProfileData } from "@/utils/profileSettingsUtils";

const validation = validateStudentProfileData(formData);
if (!validation.isValid) {
  // Handle validation errors
  console.log(validation.errors);
}
```

## Backend Integration

The endpoints are already implemented in the backend:
- Student route: `src/routes/student.ts`
- Tutor route: `src/routes/tutor.ts`
- Profile router: `src/routes/profile.ts`

## Files Modified/Created

### Frontend Files
1. `src/redux/slices/student/studentSettingsApiSlice.js` - Updated
2. `src/redux/slices/tutor/tutorSettingsApiSlice.js` - Created
3. `src/redux/slices/student/profileApiSlice.js` - Enhanced
4. `src/utils/profileSettingsUtils.js` - Created
5. `src/components/profileSettings/ProfileSettingsExample.jsx` - Created
6. `src/pages/tutor/tutorDashboard/settings/settingsProfile/TutorProfileSettings.jsx` - Updated
7. `src/pages/student/studentDashboard/settings/settingsProfile/SettingsProfile.jsx` - Updated

### Backend Files (Already Implemented)
1. `src/routes/profile.ts`
2. `src/routes/student.ts`
3. `src/routes/tutor.ts`
4. `src/controllers/student.ts`
5. `src/controllers/tutor.ts`

## Testing

To test the endpoints:

1. **Student Profile Update**:
   ```bash
   curl -X PUT "https://convolly-backend.onrender.com/api/profile/students/{studentId}" \
     -H "Authorization: Bearer {token}" \
     -H "Content-Type: application/json" \
     -d '{"firstname": "John", "lastname": "Doe"}'
   ```

2. **Tutor Profile Update**:
   ```bash
   curl -X PUT "https://convolly-backend.onrender.com/api/profile/tutors/{tutorId}" \
     -H "Authorization: Bearer {token}" \
     -H "Content-Type: application/json" \
     -d '{"firstname": "Jane", "lastname": "Smith", "basePrice": 50}'
   ```

## Notes

- Both endpoints require authentication
- Student endpoint requires student role
- Tutor endpoint requires tutor role
- Some tutor profile changes may require admin approval
- Profile images should be handled separately through the profile image endpoints
- All monetary values should be in the smallest currency unit (cents for USD)
